"""
Dynamic Ferry Crossing with True Rotating Calipers
真正动态的汽渡穿越演示 - 集成完整旋转卡壳算法

特点：
1. 船舶真正运动起来 - 实时位置更新
2. 真正使用rotating calipers算法 - 完整实现
3. 动态避让决策 - 基于实时几何分析
4. COLREGs规则动态应用 - 实时合规性检查
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Polygon, Circle, Rectangle, Arrow
import math
import time
import warnings

warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'

from src_clean.geometry.rotating_calipers import Point, ShipGeometry, RotatingCalipers

class DynamicVessel:
    """动态船舶类"""

    def __init__(self, vessel_id, initial_pos, velocity, heading, length, width,
                 vessel_type, priority, color):
        self.vessel_id = vessel_id
        self.position = initial_pos
        self.velocity = velocity
        self.heading = heading
        self.angular_velocity = 0.0
        self.length = length
        self.width = width
        self.vessel_type = vessel_type  # 'main_channel' or 'crossing'
        self.priority = priority
        self.color = color

        # 动态状态
        self.trajectory = [initial_pos]
        self.is_avoiding = False
        self.avoidance_start_time = None
        self.original_heading = heading
        self.original_velocity = velocity

        # 旋转卡壳分析结果
        self.current_risks = {}
        self.closest_distances = {}
        self.caliper_results = {}

    def update_position(self, dt):
        """更新船舶位置"""
        # 更新位置
        self.position = Point(
            self.position.x + self.velocity.x * dt,
            self.position.y + self.velocity.y * dt
        )

        # 更新航向（如果有角速度）
        if abs(self.angular_velocity) > 0.001:
            self.heading += self.angular_velocity * dt
            self.heading = self.heading % 360

            # 更新速度方向
            speed = math.sqrt(self.velocity.x**2 + self.velocity.y**2)
            heading_rad = math.radians(90 - self.heading)
            self.velocity = Point(
                speed * math.cos(heading_rad),
                speed * math.sin(heading_rad)
            )

        # 记录轨迹
        self.trajectory.append(self.position)
        if len(self.trajectory) > 100:  # 限制轨迹长度
            self.trajectory = self.trajectory[-100:]

    def get_ship_geometry(self):
        """获取船舶几何"""
        # 生成船舶轮廓顶点
        half_length = (self.length / 111000) / 2
        half_width = (self.width / 111000) / 2

        # 船舶轮廓点（相对坐标）
        relative_points = [
            Point(half_length, 0),                    # 船首
            Point(half_length * 0.7, half_width),     # 右前
            Point(-half_length * 0.3, half_width),    # 右中
            Point(-half_length, half_width * 0.6),    # 右后
            Point(-half_length, -half_width * 0.6),   # 左后
            Point(-half_length * 0.3, -half_width),   # 左中
            Point(half_length * 0.7, -half_width),    # 左前
        ]

        # 转换为全局坐标
        heading_rad = math.radians(90 - self.heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        global_vertices = []
        for point in relative_points:
            # 旋转变换
            rotated_x = point.x * cos_h - point.y * sin_h
            rotated_y = point.x * sin_h + point.y * cos_h

            global_vertices.append(Point(
                self.position.x + rotated_x,
                self.position.y + rotated_y
            ))

        return ShipGeometry(
            center=self.position,
            length=self.length / 111000,
            width=self.width / 111000,
            heading=self.heading,
            vertices=global_vertices
        )

    def apply_avoidance_maneuver(self, heading_change, speed_change, current_time):
        """应用避让机动"""
        if not self.is_avoiding:
            self.is_avoiding = True
            self.avoidance_start_time = current_time

            # 改变航向
            self.heading = (self.heading + heading_change) % 360

            # 改变速度
            current_speed = math.sqrt(self.velocity.x**2 + self.velocity.y**2)
            new_speed = max(0.00005, current_speed * (1 + speed_change))  # 最小速度限制

            heading_rad = math.radians(90 - self.heading)
            self.velocity = Point(
                new_speed * math.cos(heading_rad),
                new_speed * math.sin(heading_rad)
            )

            print(f"Vessel {self.vessel_id} applying avoidance: "
                  f"heading change {heading_change:+.1f}°, "
                  f"speed change {speed_change:+.1%}")

class DynamicFerrySimulation:
    """动态汽渡仿真"""

    def __init__(self):
        self.vessels = []
        self.calipers = RotatingCalipers()
        self.current_time = 0.0
        self.dt = 2.0  # 时间步长（秒）

        # 仿真参数
        self.safety_threshold = 0.002  # 2海里安全阈值
        self.coordination_active = False

        # 创建船舶
        self._create_vessels()

        # 设置绘图
        self.fig, self.ax = plt.subplots(figsize=(16, 12))
        self._setup_plot()

        # 动画元素
        self.vessel_patches = {}
        self.trajectory_lines = {}
        self.risk_lines = []
        self.info_text = None

    def _create_vessels(self):
        """创建船舶"""

        # 南北主航道船舶（优先权）
        # 1. 北向集装箱船
        self.vessels.append(DynamicVessel(
            vessel_id=1,
            initial_pos=Point(121.010, 30.995),
            velocity=Point(0.0, 0.00025),  # 15节北向
            heading=0.0,
            length=400.0, width=58.0,
            vessel_type='main_channel',
            priority=1,
            color='darkblue'
        ))

        # 2. 南向油轮
        self.vessels.append(DynamicVessel(
            vessel_id=2,
            initial_pos=Point(121.012, 31.020),
            velocity=Point(0.0, -0.00018),  # 11节南向
            heading=180.0,
            length=350.0, width=60.0,
            vessel_type='main_channel',
            priority=1,
            color='navy'
        ))

        # 东西穿越汽渡轮（需要避让）
        # 3. 东向汽渡轮1
        self.vessels.append(DynamicVessel(
            vessel_id=3,
            initial_pos=Point(121.000, 31.008),
            velocity=Point(0.00022, 0.0),  # 13节东向
            heading=90.0,
            length=180.0, width=28.0,
            vessel_type='crossing',
            priority=3,
            color='darkred'
        ))

        # 4. 东向汽渡轮2
        self.vessels.append(DynamicVessel(
            vessel_id=4,
            initial_pos=Point(120.998, 31.005),
            velocity=Point(0.0002, 0.0),  # 12节东向
            heading=90.0,
            length=160.0, width=25.0,
            vessel_type='crossing',
            priority=3,
            color='red'
        ))

        # 5. 西向汽渡轮
        self.vessels.append(DynamicVessel(
            vessel_id=5,
            initial_pos=Point(121.025, 31.012),
            velocity=Point(-0.00018, 0.0),  # 11节西向
            heading=270.0,
            length=170.0, width=26.0,
            vessel_type='crossing',
            priority=3,
            color='maroon'
        ))

    def _setup_plot(self):
        """设置绘图"""
        self.ax.set_xlim(120.99, 121.035)
        self.ax.set_ylim(30.985, 31.030)
        self.ax.set_xlabel('Longitude', fontsize=12)
        self.ax.set_ylabel('Latitude', fontsize=12)
        self.ax.grid(True, alpha=0.3)
        self.ax.set_aspect('equal')

        # 绘制主航道
        channel_rect = Rectangle((121.008, 30.990), 0.006, 0.035,
                                linewidth=3, edgecolor='blue', facecolor='lightblue',
                                alpha=0.3, label='Main Channel (N-S)')
        self.ax.add_patch(channel_rect)

        # 绘制航道中心线
        self.ax.plot([121.011, 121.011], [30.990, 31.025], 'b--',
                    linewidth=2, alpha=0.8, label='Channel Centerline')

        # 绘制汽渡航线
        ferry_lines_y = [31.003, 31.005, 31.008, 31.010, 31.012]
        for y in ferry_lines_y:
            self.ax.plot([120.995, 121.030], [y, y], 'r:', linewidth=1, alpha=0.6)
        self.ax.plot([120.995, 121.030], [ferry_lines_y[0], ferry_lines_y[0]], 'r:',
                    linewidth=1, alpha=0.6, label='Ferry Routes (E-W)')

        self.ax.set_title('Dynamic Ferry Crossing with Rotating Calipers\n'
                         'Real-time Collision Avoidance', fontsize=14, fontweight='bold')

        # 信息文本
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes,
                                     verticalalignment='top', fontsize=10,
                                     bbox=dict(boxstyle='round,pad=0.5',
                                              facecolor='lightcyan', alpha=0.9))

        self.ax.legend(loc='upper right')

    def analyze_with_rotating_calipers(self):
        """使用旋转卡壳算法分析"""
        analysis_start = time.time()

        # 清除之前的风险线
        for line in self.risk_lines:
            line.remove()
        self.risk_lines = []

        total_iterations = 0
        critical_pairs = []

        # 对每对船舶执行旋转卡壳分析
        for i in range(len(self.vessels)):
            for j in range(i + 1, len(self.vessels)):
                vessel1 = self.vessels[i]
                vessel2 = self.vessels[j]

                # 获取船舶几何
                geom1 = vessel1.get_ship_geometry()
                geom2 = vessel2.get_ship_geometry()

                # 执行旋转卡壳算法
                result = self.calipers.rotating_calipers_distance(
                    geom1.vertices, geom2.vertices
                )

                # 记录结果
                vessel1.caliper_results[vessel2.vessel_id] = result
                vessel2.caliper_results[vessel1.vessel_id] = result

                total_iterations += result['iterations']
                distance_nm = result['min_distance'] * 60

                # 分析风险
                if distance_nm < 4.0:  # 4海里内显示
                    critical_pairs.append({
                        'vessel1': vessel1,
                        'vessel2': vessel2,
                        'distance': distance_nm,
                        'result': result
                    })

                    # 绘制风险线
                    if result['closest_points']['ship1'] and result['closest_points']['ship2']:
                        cp1 = result['closest_points']['ship1']
                        cp2 = result['closest_points']['ship2']

                        # 根据距离设置颜色
                        if distance_nm < 1.0:
                            color = 'red'
                            width = 4
                            alpha = 0.9
                        elif distance_nm < 2.0:
                            color = 'orange'
                            width = 3
                            alpha = 0.7
                        else:
                            color = 'yellow'
                            width = 2
                            alpha = 0.5

                        line, = self.ax.plot([cp1.x, cp2.x], [cp1.y, cp2.y],
                                           color=color, linewidth=width, alpha=alpha)
                        self.risk_lines.append(line)

                        # 标记距离和卡壳角度
                        mid_x = (cp1.x + cp2.x) / 2
                        mid_y = (cp1.y + cp2.y) / 2
                        text = self.ax.annotate(
                            f'{distance_nm:.2f}nm\n∠{result["caliper_angle"]:.0f}°\n'
                            f'iter:{result["iterations"]}',
                            (mid_x, mid_y),
                            xytext=(0, 0), textcoords='offset points',
                            fontsize=8, ha='center',
                            bbox=dict(boxstyle='round,pad=0.2',
                                     facecolor=color, alpha=0.8)
                        )
                        self.risk_lines.append(text)

        analysis_time = (time.time() - analysis_start) * 1000

        return {
            'critical_pairs': critical_pairs,
            'total_iterations': total_iterations,
            'analysis_time_ms': analysis_time
        }

    def apply_colregs_and_avoidance(self, analysis_result):
        """应用COLREGs规则和避让策略"""

        for pair in analysis_result['critical_pairs']:
            vessel1 = pair['vessel1']
            vessel2 = pair['vessel2']
            distance_nm = pair['distance']

            # COLREGs规则9：穿越船舶不应妨碍主航道船舶
            if distance_nm < 2.0:  # 2海里内需要避让

                # 确定避让方
                if (vessel1.vessel_type == 'crossing' and
                    vessel2.vessel_type == 'main_channel'):
                    # 穿越船舶避让主航道船舶
                    self._apply_avoidance_to_vessel(vessel1, vessel2, distance_nm)

                elif (vessel2.vessel_type == 'crossing' and
                      vessel1.vessel_type == 'main_channel'):
                    # 穿越船舶避让主航道船舶
                    self._apply_avoidance_to_vessel(vessel2, vessel1, distance_nm)

                elif (vessel1.vessel_type == 'crossing' and
                      vessel2.vessel_type == 'crossing'):
                    # 两艘穿越船舶：右舷来船有路权
                    relative_bearing = self._calculate_relative_bearing(vessel1, vessel2)
                    if 5 <= relative_bearing <= 112.5:  # vessel2在vessel1右舷
                        self._apply_avoidance_to_vessel(vessel1, vessel2, distance_nm)
                    else:
                        self._apply_avoidance_to_vessel(vessel2, vessel1, distance_nm)

    def _apply_avoidance_to_vessel(self, avoiding_vessel, target_vessel, distance_nm):
        """对特定船舶应用避让"""

        if avoiding_vessel.is_avoiding:
            return  # 已在避让中

        # 根据距离确定避让强度
        if distance_nm < 0.5:
            heading_change = 45.0  # 紧急避让
            speed_change = -0.5    # 减速50%
        elif distance_nm < 1.0:
            heading_change = 30.0  # 大幅避让
            speed_change = -0.3    # 减速30%
        else:
            heading_change = 15.0  # 预防性避让
            speed_change = -0.1    # 减速10%

        # 确定避让方向（向右转，符合海事惯例）
        if avoiding_vessel.vessel_type == 'crossing':
            # 穿越船舶：大幅度右转
            if avoiding_vessel.heading == 90.0:  # 东向
                heading_change = +heading_change  # 右转（向南）
            else:  # 西向
                heading_change = -heading_change  # 右转（向北）

        avoiding_vessel.apply_avoidance_maneuver(heading_change, speed_change, self.current_time)

    def _calculate_relative_bearing(self, vessel1, vessel2):
        """计算相对方位"""
        dx = vessel2.position.x - vessel1.position.x
        dy = vessel2.position.y - vessel1.position.y
        bearing = math.degrees(math.atan2(dy, dx))
        relative_bearing = (90 - bearing - vessel1.heading) % 360
        return relative_bearing

    def update_frame(self, frame):
        """更新动画帧"""
        self.current_time = frame * self.dt

        # 更新所有船舶位置
        for vessel in self.vessels:
            vessel.update_position(self.dt)

        # 执行旋转卡壳分析
        analysis_result = self.analyze_with_rotating_calipers()

        # 应用避让策略
        self.apply_colregs_and_avoidance(analysis_result)

        # 更新船舶可视化
        self._update_vessel_visualization()

        # 更新信息文本
        self._update_info_text(analysis_result)

        return list(self.vessel_patches.values()) + list(self.trajectory_lines.values())

    def _update_vessel_visualization(self):
        """更新船舶可视化"""

        # 清除旧的船舶图形
        for patch in self.vessel_patches.values():
            patch.remove()
        for line in self.trajectory_lines.values():
            line.remove()

        self.vessel_patches = {}
        self.trajectory_lines = {}

        # 绘制船舶
        for vessel in self.vessels:
            # 获取船舶几何
            geom = vessel.get_ship_geometry()

            # 绘制船舶轮廓
            vertices = [(v.x, v.y) for v in geom.vertices]

            # 根据避让状态调整颜色
            color = vessel.color
            alpha = 0.9 if vessel.is_avoiding else 0.7

            patch = Polygon(vertices, closed=True, facecolor=color,
                           edgecolor='black', alpha=alpha, linewidth=2)
            self.ax.add_patch(patch)
            self.vessel_patches[vessel.vessel_id] = patch

            # 绘制轨迹
            if len(vessel.trajectory) > 1:
                traj_x = [p.x for p in vessel.trajectory]
                traj_y = [p.y for p in vessel.trajectory]
                line, = self.ax.plot(traj_x, traj_y, color=color,
                                   linewidth=1, alpha=0.5)
                self.trajectory_lines[vessel.vessel_id] = line

            # 标记船舶ID和状态
            status = "AVOIDING" if vessel.is_avoiding else "NORMAL"
            vessel_type = "MC" if vessel.vessel_type == 'main_channel' else "CR"
            self.ax.annotate(f'V{vessel.vessel_id}\n{vessel_type}\n{status}',
                           (vessel.position.x, vessel.position.y),
                           xytext=(0, 0), textcoords='offset points',
                           ha='center', va='center', fontsize=8, fontweight='bold',
                           color='white')

    def _update_info_text(self, analysis_result):
        """更新信息文本"""

        avoiding_count = sum(1 for v in self.vessels if v.is_avoiding)
        main_channel_count = sum(1 for v in self.vessels if v.vessel_type == 'main_channel')
        crossing_count = sum(1 for v in self.vessels if v.vessel_type == 'crossing')

        info_text = f"""DYNAMIC ROTATING CALIPERS ANALYSIS

Time: {self.current_time:.1f}s
Vessels: {len(self.vessels)} (MC:{main_channel_count}, CR:{crossing_count})
Avoiding: {avoiding_count}

ROTATING CALIPERS PERFORMANCE:
Critical Pairs: {len(analysis_result['critical_pairs'])}
Total Iterations: {analysis_result['total_iterations']}
Analysis Time: {analysis_result['analysis_time_ms']:.1f}ms

COLREGS RULE 9 STATUS:
Crossing vessels give way to main channel
Real-time compliance monitoring
Automatic avoidance activation

ALGORITHM FEATURES:
✓ True rotating calipers implementation
✓ O(n+m) computational complexity
✓ Sub-nautical mile precision
✓ Real-time geometric analysis"""

        self.info_text.set_text(info_text)

    def run_simulation(self):
        """运行仿真"""
        print("Starting Dynamic Ferry Crossing Simulation")
        print("="*60)
        print("Features:")
        print("- True rotating calipers algorithm")
        print("- Real-time vessel movement")
        print("- Dynamic collision avoidance")
        print("- COLREGs Rule 9 compliance")

        # 创建动画
        anim = animation.FuncAnimation(
            self.fig, self.update_frame, frames=150,
            interval=200, blit=False, repeat=True
        )

        plt.tight_layout()
        plt.show()

        return anim

def main():
    """主函数"""
    print("Dynamic Ferry Crossing with True Rotating Calipers")
    print("="*80)
    print("Real-time Multi-Vessel Collision Avoidance Simulation")

    # 创建并运行仿真
    simulation = DynamicFerrySimulation()
    anim = simulation.run_simulation()

    print("\nSimulation Features Demonstrated:")
    print("✓ Vessels actually moving in real-time")
    print("✓ True rotating calipers algorithm integration")
    print("✓ Dynamic collision risk assessment")
    print("✓ Automatic COLREGs Rule 9 application")
    print("✓ Real-time avoidance maneuver execution")
    print("✓ Performance metrics display")

if __name__ == "__main__":
    main()
