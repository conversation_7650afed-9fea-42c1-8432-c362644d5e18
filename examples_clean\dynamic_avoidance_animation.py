"""
Dynamic Avoidance Animation Demo
动态避让动画演示 - 实时展示旋转卡壳算法的动态避碰效果
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Polygon, Circle
import math
import warnings

# Suppress warnings and set backend
warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend

from src_clean.geometry.rotating_calipers import Point, ShipGeometry, RotatingCalipers
from src_clean.coordination.multi_ship_coordinator import ShipType, NavigationArea
from src_clean.animation.dynamic_animator import DynamicAvoidanceAnimator, DynamicShipState

def create_dynamic_scenario():
    """创建动态场景"""
    ships = []
    
    # 航道内的大型集装箱船
    ship1 = DynamicShipState(
        mmsi=123456789,
        x=121.002, y=31.005,
        heading=90.0,  # 正东向
        speed_knots=14.0,
        length=350.0, width=45.0,
        ship_type=ShipType.CHANNEL_BOUND,
        navigation_area=NavigationArea.MAIN_CHANNEL,
        priority_level=1,
        color='blue',
        trajectory_x=[], trajectory_y=[],
        predicted_x=[], predicted_y=[]
    )
    ships.append(ship1)
    
    # 穿越航道的散货船
    ship2 = DynamicShipState(
        mmsi=987654321,
        x=121.010, y=30.998,
        heading=45.0,  # 东北向穿越
        speed_knots=10.0,
        length=280.0, width=40.0,
        ship_type=ShipType.CROSSING,
        navigation_area=NavigationArea.CROSSING_AREA,
        priority_level=3,
        color='red',
        trajectory_x=[], trajectory_y=[],
        predicted_x=[], predicted_y=[]
    )
    ships.append(ship2)
    
    # 另一艘穿越船舶
    ship3 = DynamicShipState(
        mmsi=555666777,
        x=120.996, y=31.012,
        heading=270.0,  # 正西向穿越
        speed_knots=8.0,
        length=250.0, width=35.0,
        ship_type=ShipType.CROSSING,
        navigation_area=NavigationArea.CROSSING_AREA,
        priority_level=3,
        color='green',
        trajectory_x=[], trajectory_y=[],
        predicted_x=[], predicted_y=[]
    )
    ships.append(ship3)
    
    return ships

def run_dynamic_animation():
    """运行动态动画"""
    print(f"\n🎬 Running Dynamic Avoidance Animation")
    print("="*60)
    
    # 创建场景
    ships = create_dynamic_scenario()
    
    print(f"📋 Scenario: Channel Crossing with Dynamic Avoidance")
    print(f"🚢 Ships: {len(ships)}")
    
    for ship in ships:
        print(f"   Ship {ship.mmsi}: {ship.ship_type.value}, "
              f"{ship.length}m×{ship.width}m, "
              f"{ship.speed_knots:.1f}kts, "
              f"heading {ship.heading:.0f}°")
    
    # 创建动画器
    animator = DynamicAvoidanceAnimator(figsize=(16, 10))
    
    # 添加船舶
    for ship in ships:
        animator.add_ship(ship)
    
    print(f"\n🎯 Animation Features:")
    print(f"   • Real-time rotating calipers collision detection")
    print(f"   • Dynamic multi-ship coordination")
    print(f"   • COLREGs rules automatic application")
    print(f"   • Trajectory prediction and visualization")
    print(f"   • Safety zone monitoring")
    
    # 创建动画
    try:
        print(f"\n🎬 Generating dynamic animation...")
        anim = animator.create_animation(frames=100, interval=500)
        
        # 保存为GIF
        output_filename = 'dynamic_avoidance_animation.gif'
        print(f"💾 Saving animation as {output_filename}...")
        anim.save(output_filename, writer='pillow', fps=2, dpi=100)
        
        print(f"✅ Animation saved successfully!")
        print(f"📊 Animation Details:")
        print(f"   • Duration: ~50 seconds")
        print(f"   • Frame rate: 2 FPS")
        print(f"   • Dynamic coordination triggers")
        print(f"   • Real-time avoidance maneuvers")
        
        return True
        
    except Exception as e:
        print(f"❌ Animation generation failed: {e}")
        return False

def create_simple_static_demo():
    """创建简化的静态演示"""
    print(f"\n🎨 Creating Static Demonstration")
    print("="*50)
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 绘制航道
    from matplotlib.patches import Rectangle
    channel_rect = Rectangle((121.000, 31.000), 0.020, 0.010,
                            linewidth=2, edgecolor='blue', facecolor='lightblue',
                            alpha=0.2, label='Navigation Channel')
    ax.add_patch(channel_rect)
    
    # 航道中心线
    ax.plot([121.000, 121.020], [31.005, 31.005], 'b--', 
           linewidth=2, alpha=0.7, label='Channel Centerline')
    
    # 创建船舶
    ships = create_dynamic_scenario()
    
    # 绘制船舶
    ship_colors = {'blue': 'Channel-bound', 'red': 'Crossing 1', 'green': 'Crossing 2'}
    
    for ship in ships:
        # 创建船舶几何
        geometry = ShipGeometry(
            center=Point(ship.x, ship.y),
            length=ship.length / 111000,
            width=ship.width / 111000,
            heading=ship.heading,
            vertices=[]
        )
        
        # 绘制船舶轮廓
        vertices = [(v.x, v.y) for v in geometry.vertices]
        x_coords, y_coords = zip(*vertices)
        
        ax.plot(x_coords + (x_coords[0],), y_coords + (y_coords[0],), 
               color=ship.color, linewidth=2, alpha=0.8)
        ax.fill(x_coords, y_coords, color=ship.color, alpha=0.3, 
               label=ship_colors.get(ship.color, f'Ship {ship.mmsi}'))
        
        # 标记船舶中心
        ax.scatter(ship.x, ship.y, s=100, color=ship.color, marker='o', zorder=10)
        ax.annotate(f'Ship {ship.mmsi}', (ship.x, ship.y), 
                   xytext=(5, 5), textcoords='offset points',
                   fontsize=10, fontweight='bold')
        
        # 绘制航向箭头
        arrow_length = 0.003
        math_angle = math.radians(90 - ship.heading)
        ax.arrow(ship.x, ship.y,
                arrow_length * math.cos(math_angle),
                arrow_length * math.sin(math_angle),
                head_width=0.0005, head_length=0.0003, 
                fc=ship.color, ec=ship.color, alpha=0.8)
    
    # 计算并显示距离
    calipers = RotatingCalipers()
    
    for i in range(len(ships)):
        for j in range(i + 1, len(ships)):
            ship1 = ships[i]
            ship2 = ships[j]
            
            # 创建几何
            geom1 = ShipGeometry(
                center=Point(ship1.x, ship1.y),
                length=ship1.length / 111000,
                width=ship1.width / 111000,
                heading=ship1.heading,
                vertices=[]
            )
            geom2 = ShipGeometry(
                center=Point(ship2.x, ship2.y),
                length=ship2.length / 111000,
                width=ship2.width / 111000,
                heading=ship2.heading,
                vertices=[]
            )
            
            # 计算距离
            distance_result = calipers.rotating_calipers_distance(
                geom1.vertices, geom2.vertices
            )
            
            closest_points = distance_result['closest_points']
            if closest_points['ship1'] and closest_points['ship2']:
                cp1 = closest_points['ship1']
                cp2 = closest_points['ship2']
                
                distance_nm = distance_result['min_distance'] * 60
                
                # 根据距离设置颜色
                if distance_nm < 2.0:
                    color = 'red'
                    alpha = 0.8
                elif distance_nm < 4.0:
                    color = 'orange'
                    alpha = 0.6
                else:
                    color = 'green'
                    alpha = 0.4
                
                # 绘制距离线
                ax.plot([cp1.x, cp2.x], [cp1.y, cp2.y], 
                       color=color, linewidth=2, alpha=alpha)
                
                # 标记距离
                mid_x = (cp1.x + cp2.x) / 2
                mid_y = (cp1.y + cp2.y) / 2
                ax.annotate(f'{distance_nm:.2f}nm', (mid_x, mid_y),
                           xytext=(0, 0), textcoords='offset points',
                           fontsize=9, ha='center', fontweight='bold',
                           bbox=dict(boxstyle='round,pad=0.3', 
                                    facecolor=color, alpha=0.8))
    
    # 设置图形属性
    ax.set_xlim(120.99, 121.03)
    ax.set_ylim(30.99, 31.02)
    ax.set_xlabel('Longitude', fontsize=12)
    ax.set_ylabel('Latitude', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    ax.legend(loc='upper right')
    
    ax.set_title('Dynamic Avoidance System - Initial State\n'
                'Rotating Calipers Multi-Ship Coordination', 
                fontsize=14, fontweight='bold')
    
    # 添加信息框
    info_text = f"""DYNAMIC AVOIDANCE SYSTEM

🚢 Ships: {len(ships)}
🔄 Algorithm: Rotating Calipers
⚖️  Rules: COLREGs Compliant
🎬 Animation: Real-time

FEATURES:
• Precise geometric collision detection
• Dynamic coordination triggers
• COLREGs rules automatic application
• Real-time trajectory prediction
• Multi-ship conflict resolution

DISTANCE CODING:
Red: < 2nm (High Risk)
Orange: 2-4nm (Medium Risk)
Green: > 4nm (Safe)"""
    
    ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
           verticalalignment='bottom', fontsize=9, fontfamily='monospace',
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightcyan', alpha=0.9))
    
    plt.tight_layout()
    plt.savefig('dynamic_avoidance_static.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ Static demonstration created!")
    print(f"📁 Output: dynamic_avoidance_static.png")

def main():
    """主函数"""
    print("🎬 Dynamic Avoidance Animation System")
    print("="*70)
    print("Real-time Rotating Calipers Coordination")
    
    # 尝试创建动画
    animation_success = run_dynamic_animation()
    
    # 创建静态演示
    create_simple_static_demo()
    
    print(f"\n🎉 Dynamic Avoidance Demo Summary")
    print("="*50)
    
    if animation_success:
        print(f"✅ Dynamic animation: SUCCESS")
        print(f"📁 Generated: dynamic_avoidance_animation.gif")
    else:
        print(f"⚠️  Dynamic animation: FAILED (static demo available)")
    
    print(f"✅ Static demonstration: SUCCESS")
    print(f"📁 Generated: dynamic_avoidance_static.png")
    
    print(f"\n🚀 Key Technical Achievements:")
    print(f"   ✅ Real-time rotating calipers collision detection")
    print(f"   ✅ Dynamic multi-ship coordination triggers")
    print(f"   ✅ Automatic COLREGs rules application")
    print(f"   ✅ Priority-based conflict resolution")
    print(f"   ✅ Trajectory prediction and visualization")
    print(f"   ✅ Safety zone monitoring and alerts")
    print(f"   ✅ Coordinated avoidance maneuvers")
    
    print(f"\n📈 Dynamic Features Demonstrated:")
    print(f"   🔄 Real-time coordination activation")
    print(f"   🎯 Precision geometric calculations")
    print(f"   ⚖️  COLREGs compliance verification")
    print(f"   🚢 Multi-ship group coordination")
    print(f"   📊 Performance metrics tracking")
    print(f"   🎬 Smooth animation transitions")
    
    print(f"\n✅ Dynamic avoidance animation demo completed!")

if __name__ == "__main__":
    main()
