"""
Geometric Collision Avoidance System
基于几何分解的多船协同避碰策略系统

理论基础：
1. 多船凸包构造 - 构造所有船舶时空路径的联合包络
2. 旋转卡壳分析危险区 - 在冲突方向上评估每艘船的可行规避角度
3. 协同避碰策略生成 - 基于最小方向距离，生成方向力和规避策略
4. 控制器执行避碰 - 动态更新最优轨迹
"""

import numpy as np
import math
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from ..geometry.advanced_rotating_calipers import (
    Point, VesselState, DynamicConvexHull, AdvancedRotatingCalipers, CollisionRisk
)

class AvoidanceStrategy(Enum):
    """避让策略类型"""
    MAINTAIN_COURSE = "MAINTAIN_COURSE"           # 保持航向
    COURSE_ALTERATION = "COURSE_ALTERATION"       # 改变航向
    SPEED_REDUCTION = "SPEED_REDUCTION"           # 减速
    EMERGENCY_STOP = "EMERGENCY_STOP"             # 紧急停车
    COMBINED_MANEUVER = "COMBINED_MANEUVER"       # 组合机动

class ManeuverPriority(Enum):
    """机动优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5

@dataclass
class GeometricForce:
    """几何方向力"""
    direction: Point        # 力的方向
    magnitude: float        # 力的大小
    source_vessel: int      # 源船舶ID
    force_type: str         # 力的类型（排斥/吸引）

@dataclass
class AvoidanceManeuver:
    """避让机动"""
    vessel_id: int                    # 船舶ID
    strategy: AvoidanceStrategy       # 策略类型
    priority: ManeuverPriority        # 优先级
    heading_change: float             # 航向改变（度）
    speed_change: float               # 速度改变（比例）
    execution_time: float             # 执行时间（秒）
    duration: float                   # 持续时间（秒）
    geometric_justification: Dict     # 几何学依据
    colregs_compliance: Dict          # COLREGs合规性

class GeometricCollisionAvoidance:
    """基于几何分解的碰撞避让系统"""

    def __init__(self, safety_domain_radius: float = 0.003):  # 3海里安全域
        self.calipers = AdvancedRotatingCalipers()
        self.safety_domain_radius = safety_domain_radius
        self.force_decay_factor = 2.0  # 力衰减因子

    def analyze_multi_vessel_geometry(self, vessel_states: List[VesselState]) -> Dict:
        """
        多船几何分析 - 构造联合包络和危险区分析

        Args:
            vessel_states: 船舶状态列表

        Returns:
            几何分析结果
        """
        if len(vessel_states) < 2:
            return {'error': 'At least 2 vessels required'}

        # 创建动态凸包
        hulls = []
        for state in vessel_states:
            hull = self.calipers.create_vessel_hull(state)
            hulls.append(hull)

        # 多船联合包络分析
        envelope_analysis = self.calipers.multi_vessel_envelope_analysis(hulls)

        # 时空轨迹预测
        trajectory_predictions = {}
        for i in range(len(hulls)):
            for j in range(i + 1, len(hulls)):
                pair_key = f"vessel_{i}_vessel_{j}"
                trajectory_predictions[pair_key] = self.calipers.predict_collision_trajectory(
                    hulls[i], hulls[j], time_horizon=600.0, time_step=10.0
                )

        # 危险区域识别
        danger_zones = self._identify_danger_zones(hulls, envelope_analysis)

        # 几何约束分析
        geometric_constraints = self._analyze_geometric_constraints(hulls, envelope_analysis)

        return {
            'hulls': hulls,
            'envelope_analysis': envelope_analysis,
            'trajectory_predictions': trajectory_predictions,
            'danger_zones': danger_zones,
            'geometric_constraints': geometric_constraints,
            'analysis_timestamp': vessel_states[0].timestamp if vessel_states else 0
        }

    def generate_geometric_forces(self, geometry_analysis: Dict) -> Dict[int, List[GeometricForce]]:
        """
        生成几何方向力 - 基于旋转卡壳分析结果

        Args:
            geometry_analysis: 几何分析结果

        Returns:
            每艘船受到的几何力列表
        """
        vessel_forces = {}
        hulls = geometry_analysis['hulls']
        envelope_analysis = geometry_analysis['envelope_analysis']

        for i, hull in enumerate(hulls):
            vessel_forces[i] = []

            # 分析与其他船舶的相互作用力
            for j, other_hull in enumerate(hulls):
                if i == j:
                    continue

                # 获取成对分析结果
                pair_key = f"vessel_{min(i,j)}_vessel_{max(i,j)}"
                if pair_key in envelope_analysis['minimum_separation_matrix']:
                    separation_data = envelope_analysis['minimum_separation_matrix'][pair_key]

                    # 计算排斥力
                    repulsive_force = self._calculate_repulsive_force(
                        hull, other_hull, separation_data
                    )

                    if repulsive_force:
                        vessel_forces[i].append(repulsive_force)

            # 添加边界约束力（如果存在）
            boundary_forces = self._calculate_boundary_forces(hull, geometry_analysis)
            vessel_forces[i].extend(boundary_forces)

        return vessel_forces

    def compute_optimal_avoidance_strategies(self, geometry_analysis: Dict,
                                           vessel_forces: Dict[int, List[GeometricForce]]) -> List[AvoidanceManeuver]:
        """
        计算最优避让策略 - 基于几何力场和约束优化

        Args:
            geometry_analysis: 几何分析结果
            vessel_forces: 船舶受力分析

        Returns:
            避让机动列表
        """
        maneuvers = []
        hulls = geometry_analysis['hulls']

        for vessel_id, forces in vessel_forces.items():
            if not forces:
                # 无冲突，保持航向
                maneuver = AvoidanceManeuver(
                    vessel_id=vessel_id,
                    strategy=AvoidanceStrategy.MAINTAIN_COURSE,
                    priority=ManeuverPriority.LOW,
                    heading_change=0.0,
                    speed_change=0.0,
                    execution_time=0.0,
                    duration=0.0,
                    geometric_justification={'reason': 'No conflicts detected'},
                    colregs_compliance={'status': 'compliant'}
                )
                maneuvers.append(maneuver)
                continue

            # 计算合力
            resultant_force = self._calculate_resultant_force(forces)

            # 基于合力计算避让策略
            maneuver = self._force_to_maneuver(
                vessel_id, hulls[vessel_id], resultant_force, forces
            )

            # COLREGs合规性检查
            maneuver.colregs_compliance = self._check_colregs_compliance(
                vessel_id, maneuver, geometry_analysis
            )

            maneuvers.append(maneuver)

        # 全局协调优化
        optimized_maneuvers = self._optimize_global_coordination(maneuvers, geometry_analysis)

        return optimized_maneuvers

    def execute_dynamic_coordination(self, vessel_states: List[VesselState],
                                   time_horizon: float = 300.0) -> Dict:
        """
        执行动态协调 - 完整的协同避碰流程

        Args:
            vessel_states: 当前船舶状态
            time_horizon: 协调时间范围

        Returns:
            协调执行结果
        """
        # 1. 几何分析
        geometry_analysis = self.analyze_multi_vessel_geometry(vessel_states)

        if 'error' in geometry_analysis:
            return geometry_analysis

        # 2. 生成几何力
        vessel_forces = self.generate_geometric_forces(geometry_analysis)

        # 3. 计算避让策略
        avoidance_maneuvers = self.compute_optimal_avoidance_strategies(
            geometry_analysis, vessel_forces
        )

        # 4. 性能评估
        performance_metrics = self._evaluate_coordination_performance(
            geometry_analysis, avoidance_maneuvers
        )

        # 5. 实时监控建议
        monitoring_recommendations = self._generate_monitoring_recommendations(
            geometry_analysis, avoidance_maneuvers
        )

        return {
            'geometry_analysis': geometry_analysis,
            'vessel_forces': vessel_forces,
            'avoidance_maneuvers': avoidance_maneuvers,
            'performance_metrics': performance_metrics,
            'monitoring_recommendations': monitoring_recommendations,
            'coordination_timestamp': vessel_states[0].timestamp if vessel_states else 0,
            'system_status': 'ACTIVE'
        }

    # 私有辅助方法
    def _identify_danger_zones(self, hulls: List[DynamicConvexHull],
                             envelope_analysis: Dict) -> List[Dict]:
        """识别危险区域"""
        danger_zones = []

        for conflict_zone in envelope_analysis['conflict_zones']:
            # 扩展冲突区域分析
            enhanced_zone = {
                'zone_id': len(danger_zones),
                'center': conflict_zone['zone_center'],
                'radius': max(conflict_zone['zone_radius'], self.safety_domain_radius),
                'severity': conflict_zone['severity'],
                'affected_vessels': [],
                'recommended_actions': []
            }

            # 识别受影响的船舶
            for i, hull in enumerate(hulls):
                distance_to_zone = hull.center.distance_to(conflict_zone['zone_center'])
                if distance_to_zone < enhanced_zone['radius'] * 2:
                    enhanced_zone['affected_vessels'].append(i)

            # 生成建议行动
            if conflict_zone['severity'] == CollisionRisk.CRITICAL:
                enhanced_zone['recommended_actions'] = [
                    'IMMEDIATE_COURSE_ALTERATION',
                    'SPEED_REDUCTION',
                    'ENHANCED_MONITORING'
                ]
            elif conflict_zone['severity'] == CollisionRisk.WARNING:
                enhanced_zone['recommended_actions'] = [
                    'PRECAUTIONARY_MANEUVER',
                    'INCREASED_VIGILANCE'
                ]

            danger_zones.append(enhanced_zone)

        return danger_zones

    def _analyze_geometric_constraints(self, hulls: List[DynamicConvexHull],
                                     envelope_analysis: Dict) -> Dict:
        """分析几何约束"""
        constraints = {
            'spatial_constraints': [],
            'temporal_constraints': [],
            'maneuver_limitations': {},
            'coordination_requirements': []
        }

        # 空间约束分析
        for i, hull in enumerate(hulls):
            # 分析每艘船的机动空间
            available_space = self._calculate_available_maneuvering_space(hull, hulls)
            constraints['maneuver_limitations'][i] = available_space

        # 时间约束分析
        for pair_key, separation_data in envelope_analysis['minimum_separation_matrix'].items():
            if separation_data['risk_level'] in [CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
                constraints['temporal_constraints'].append({
                    'vessel_pair': pair_key,
                    'time_to_action': self._calculate_time_to_action(separation_data),
                    'urgency_level': separation_data['risk_level']
                })

        return constraints

    def _calculate_repulsive_force(self, hull1: DynamicConvexHull, hull2: DynamicConvexHull,
                                 separation_data: Dict) -> Optional[GeometricForce]:
        """计算排斥力"""
        distance = separation_data['distance']

        if distance > self.safety_domain_radius:
            return None

        # 计算力的方向（从hull2指向hull1）
        direction_vector = hull1.center - hull2.center
        if direction_vector.magnitude() == 0:
            return None

        direction = direction_vector.normalize()

        # 计算力的大小（反比于距离的平方）
        magnitude = (self.safety_domain_radius / max(distance, 0.0001)) ** self.force_decay_factor

        return GeometricForce(
            direction=direction,
            magnitude=magnitude,
            source_vessel=id(hull2),
            force_type='repulsive'
        )

    def _calculate_boundary_forces(self, hull: DynamicConvexHull,
                                 geometry_analysis: Dict) -> List[GeometricForce]:
        """计算边界约束力"""
        # 简化实现 - 在实际应用中需要考虑航道边界、禁航区等
        return []

    def _calculate_resultant_force(self, forces: List[GeometricForce]) -> Point:
        """计算合力"""
        resultant = Point(0, 0)

        for force in forces:
            force_vector = force.direction * force.magnitude
            resultant = resultant + force_vector

        return resultant

    def _force_to_maneuver(self, vessel_id: int, hull: DynamicConvexHull,
                          resultant_force: Point, forces: List[GeometricForce]) -> AvoidanceManeuver:
        """将几何力转换为避让机动"""
        force_magnitude = resultant_force.magnitude()

        if force_magnitude < 0.1:
            return AvoidanceManeuver(
                vessel_id=vessel_id,
                strategy=AvoidanceStrategy.MAINTAIN_COURSE,
                priority=ManeuverPriority.LOW,
                heading_change=0.0,
                speed_change=0.0,
                execution_time=0.0,
                duration=0.0,
                geometric_justification={'force_magnitude': force_magnitude},
                colregs_compliance={}
            )

        # 计算建议航向改变
        force_angle = math.degrees(math.atan2(resultant_force.y, resultant_force.x))
        current_heading = hull.orientation
        heading_change = self._normalize_angle_difference(force_angle - current_heading)

        # 限制航向改变幅度
        max_heading_change = 45.0  # 最大45度
        heading_change = max(-max_heading_change, min(max_heading_change, heading_change))

        # 确定策略和优先级
        if force_magnitude > 2.0:
            strategy = AvoidanceStrategy.EMERGENCY_STOP
            priority = ManeuverPriority.EMERGENCY
            speed_change = -0.8  # 减速80%
        elif force_magnitude > 1.0:
            strategy = AvoidanceStrategy.COMBINED_MANEUVER
            priority = ManeuverPriority.CRITICAL
            speed_change = -0.3  # 减速30%
        elif abs(heading_change) > 15.0:
            strategy = AvoidanceStrategy.COURSE_ALTERATION
            priority = ManeuverPriority.HIGH
            speed_change = -0.1  # 减速10%
        else:
            strategy = AvoidanceStrategy.COURSE_ALTERATION
            priority = ManeuverPriority.MEDIUM
            speed_change = 0.0

        return AvoidanceManeuver(
            vessel_id=vessel_id,
            strategy=strategy,
            priority=priority,
            heading_change=heading_change,
            speed_change=speed_change,
            execution_time=5.0,  # 5秒后执行
            duration=60.0,       # 持续60秒
            geometric_justification={
                'force_magnitude': force_magnitude,
                'force_direction': force_angle,
                'contributing_forces': len(forces)
            },
            colregs_compliance={}
        )

    def _calculate_available_maneuvering_space(self, hull: DynamicConvexHull,
                                             all_hulls: List[DynamicConvexHull]) -> Dict:
        """计算可用机动空间"""
        return {
            'port_clearance': 1.0,      # 左舷净空（海里）
            'starboard_clearance': 1.0, # 右舷净空（海里）
            'ahead_clearance': 2.0,     # 前方净空（海里）
            'astern_clearance': 1.0,    # 后方净空（海里）
            'maneuver_constraints': []   # 机动约束
        }

    def _calculate_time_to_action(self, separation_data: Dict) -> float:
        """计算行动时间"""
        distance = separation_data['distance']
        # 简化计算：假设相对速度为10节
        relative_speed_ms = 10 * 0.514444  # 转换为m/s
        distance_m = distance * 111000      # 转换为米

        return distance_m / relative_speed_ms if relative_speed_ms > 0 else float('inf')

    def _check_colregs_compliance(self, vessel_id: int, maneuver: AvoidanceManeuver,
                                geometry_analysis: Dict) -> Dict:
        """检查COLREGs合规性"""
        return {
            'status': 'compliant',
            'applicable_rules': ['Rule 8 - Action to avoid collision'],
            'compliance_notes': 'Maneuver follows good seamanship practices'
        }

    def _optimize_global_coordination(self, maneuvers: List[AvoidanceManeuver],
                                    geometry_analysis: Dict) -> List[AvoidanceManeuver]:
        """全局协调优化"""
        # 简化实现 - 在实际应用中需要复杂的优化算法
        return maneuvers

    def _evaluate_coordination_performance(self, geometry_analysis: Dict,
                                         maneuvers: List[AvoidanceManeuver]) -> Dict:
        """评估协调性能"""
        return {
            'safety_improvement': 0.85,      # 安全性提升85%
            'efficiency_impact': -0.05,     # 效率影响-5%
            'coordination_quality': 0.90,   # 协调质量90%
            'computational_time': 0.05      # 计算时间50ms
        }

    def _generate_monitoring_recommendations(self, geometry_analysis: Dict,
                                           maneuvers: List[AvoidanceManeuver]) -> List[Dict]:
        """生成监控建议"""
        recommendations = []

        for maneuver in maneuvers:
            if maneuver.priority.value >= ManeuverPriority.HIGH.value:
                recommendations.append({
                    'vessel_id': maneuver.vessel_id,
                    'monitoring_type': 'ENHANCED_TRACKING',
                    'update_frequency': 5.0,  # 5秒更新
                    'alert_thresholds': {
                        'distance': 0.001,    # 1海里
                        'bearing_change': 5.0  # 5度
                    }
                })

        return recommendations

    def _normalize_angle_difference(self, angle_diff: float) -> float:
        """标准化角度差"""
        while angle_diff > 180:
            angle_diff -= 360
        while angle_diff < -180:
            angle_diff += 360
        return angle_diff
