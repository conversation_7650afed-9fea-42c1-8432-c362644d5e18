"""
Rotating Calipers Algorithm for Ship Collision Detection
旋转卡壳算法 - 船舶碰撞检测核心算法
"""

import numpy as np
import math
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

@dataclass
class Point:
    """二维点"""
    x: float
    y: float

    def __add__(self, other):
        return Point(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return Point(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar):
        return Point(self.x * scalar, self.y * scalar)

    def distance_to(self, other):
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)

@dataclass
class ShipGeometry:
    """船舶几何"""
    center: Point
    length: float
    width: float
    heading: float
    vertices: List[Point]

    def __post_init__(self):
        if not self.vertices:
            self.vertices = self._generate_ship_hull()

    def _generate_ship_hull(self) -> List[Point]:
        """生成船舶凸包 - 船首指向航向方向"""
        half_length = self.length / 2
        half_width = self.width / 2

        # 船舶轮廓点（相对坐标）- 船首指向+X方向
        relative_points = [
            Point(half_length, 0),                    # 船首（正前方）
            Point(half_length * 0.7, half_width * 0.8),     # 右前
            Point(-half_length * 0.3, half_width),          # 右中
            Point(-half_length, half_width * 0.6),          # 右后
            Point(-half_length, -half_width * 0.6),         # 左后
            Point(-half_length * 0.3, -half_width),         # 左中
            Point(half_length * 0.7, -half_width * 0.8),    # 左前
        ]

        # 将航向转换为数学角度
        # 航海中：北=0°，东=90°，南=180°，西=270°
        # 数学中：东=0°，北=90°，西=180°，南=270°
        math_angle = 90 - self.heading  # 转换为数学角度
        heading_rad = math.radians(math_angle)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        # 旋转并平移到实际位置
        vertices = []
        for p in relative_points:
            # 旋转变换：使船首指向正确的航向
            rotated_x = p.x * cos_h - p.y * sin_h
            rotated_y = p.x * sin_h + p.y * cos_h
            vertices.append(Point(
                self.center.x + rotated_x,
                self.center.y + rotated_y
            ))

        return vertices

class RotatingCalipers:
    """旋转卡壳算法实现"""

    def __init__(self):
        self.tolerance = 1e-10

    def rotating_calipers_distance(self, hull1: List[Point], hull2: List[Point]) -> Dict:
        """
        真正的旋转卡壳算法实现 - 计算两个凸包间的最小距离

        Args:
            hull1, hull2: 船舶凸包顶点列表

        Returns:
            {
                'min_distance': float,      # 最小距离
                'closest_points': dict,     # 最近点对
                'contact_type': str,        # 接触类型
                'separation_vector': Point, # 分离向量
                'caliper_angle': float,     # 卡壳角度
                'iterations': int           # 迭代次数
            }
        """
        if not hull1 or not hull2:
            return self._empty_result()

        # 确保凸包是逆时针方向
        hull1 = self._ensure_ccw(hull1)
        hull2 = self._ensure_ccw(hull2)

        n1, n2 = len(hull1), len(hull2)
        if n1 < 3 or n2 < 3:
            return self._brute_force_distance(hull1, hull2)

        # 初始化旋转卡壳
        min_distance = float('inf')
        closest_point1 = None
        closest_point2 = None
        contact_type = "separated"
        optimal_angle = 0.0
        iterations = 0

        # 找到初始对踵点对
        i = self._find_rightmost_point(hull1)
        j = self._find_leftmost_point(hull2)

        start_i, start_j = i, j
        current_angle = 0.0

        # 旋转卡壳主循环
        while current_angle < 2 * math.pi and iterations < n1 + n2:
            iterations += 1

            # 计算当前点对距离
            current_dist = hull1[i].distance_to(hull2[j])
            if current_dist < min_distance:
                min_distance = current_dist
                closest_point1 = hull1[i]
                closest_point2 = hull2[j]
                contact_type = "vertex-vertex"
                optimal_angle = math.degrees(current_angle)

            # 检查点到边的距离
            edge_distances = self._check_edge_distances(hull1, hull2, i, j)
            for edge_dist in edge_distances:
                if edge_dist['distance'] < min_distance:
                    min_distance = edge_dist['distance']
                    closest_point1 = edge_dist['point1']
                    closest_point2 = edge_dist['point2']
                    contact_type = edge_dist['type']
                    optimal_angle = math.degrees(current_angle)

            # 计算下一步旋转
            next_i = (i + 1) % n1
            next_j = (j + 1) % n2

            # 计算边向量与当前卡壳方向的夹角
            edge1 = hull1[next_i] - hull1[i]
            edge2 = hull2[next_j] - hull2[j]

            # 当前卡壳方向
            caliper_dir = Point(math.cos(current_angle), math.sin(current_angle))

            # 计算边与卡壳方向的夹角
            angle1 = self._vector_angle(edge1, caliper_dir)
            angle2 = self._vector_angle(edge2, caliper_dir)

            # 选择较小的角度进行旋转
            if angle1 <= angle2:
                current_angle += angle1
                i = next_i
                if angle1 == angle2:  # 同时旋转
                    j = next_j
            else:
                current_angle += angle2
                j = next_j

            # 检查是否完成一圈
            if i == start_i and j == start_j and current_angle > 0:
                break

        # 计算分离向量
        separation_vector = None
        if closest_point1 and closest_point2:
            separation_vector = closest_point2 - closest_point1

        return {
            'min_distance': min_distance,
            'closest_points': {
                'ship1': closest_point1,
                'ship2': closest_point2
            },
            'contact_type': contact_type,
            'separation_vector': separation_vector,
            'caliper_angle': optimal_angle,
            'iterations': iterations
        }

    def predict_collision_time(self, ship1_geom: ShipGeometry, ship1_velocity: Point,
                              ship2_geom: ShipGeometry, ship2_velocity: Point,
                              time_horizon: float = 300.0) -> Dict:
        """
        预测碰撞时间

        Args:
            ship1_geom, ship2_geom: 船舶几何
            ship1_velocity, ship2_velocity: 船舶速度
            time_horizon: 预测时间范围（秒）

        Returns:
            预测结果字典
        """
        min_distance = float('inf')
        collision_time = None
        closest_approach_time = None

        # 时间步长
        dt = 2.0  # 2秒
        steps = int(time_horizon / dt)

        for step in range(steps):
            t = step * dt

            # 预测未来位置
            future_center1 = Point(
                ship1_geom.center.x + ship1_velocity.x * t,
                ship1_geom.center.y + ship1_velocity.y * t
            )
            future_center2 = Point(
                ship2_geom.center.x + ship2_velocity.x * t,
                ship2_geom.center.y + ship2_velocity.y * t
            )

            # 创建未来几何
            future_geom1 = ShipGeometry(
                center=future_center1,
                length=ship1_geom.length,
                width=ship1_geom.width,
                heading=ship1_geom.heading,
                vertices=[]
            )
            future_geom2 = ShipGeometry(
                center=future_center2,
                length=ship2_geom.length,
                width=ship2_geom.width,
                heading=ship2_geom.heading,
                vertices=[]
            )

            # 计算距离
            distance_result = self.rotating_calipers_distance(
                future_geom1.vertices, future_geom2.vertices
            )

            distance = distance_result['min_distance']

            if distance < min_distance:
                min_distance = distance
                closest_approach_time = t

            # 检查碰撞
            if distance < 0.0001:  # 碰撞阈值
                collision_time = t
                break

        return {
            'collision_predicted': collision_time is not None,
            'collision_time': collision_time,
            'closest_approach_time': closest_approach_time,
            'min_predicted_distance': min_distance
        }

    def _ensure_ccw(self, hull: List[Point]) -> List[Point]:
        """确保凸包顶点按逆时针方向排列"""
        if len(hull) < 3:
            return hull

        # 计算有向面积
        area = 0
        n = len(hull)
        for i in range(n):
            j = (i + 1) % n
            area += (hull[j].x - hull[i].x) * (hull[j].y + hull[i].y)

        # 如果面积为正，则是顺时针，需要反转
        if area > 0:
            return hull[::-1]
        return hull

    def _find_rightmost_point(self, hull: List[Point]) -> int:
        """找到最右边的点"""
        rightmost_idx = 0
        for i in range(1, len(hull)):
            if hull[i].x > hull[rightmost_idx].x:
                rightmost_idx = i
        return rightmost_idx

    def _find_leftmost_point(self, hull: List[Point]) -> int:
        """找到最左边的点"""
        leftmost_idx = 0
        for i in range(1, len(hull)):
            if hull[i].x < hull[leftmost_idx].x:
                leftmost_idx = i
        return leftmost_idx

    def _point_to_edge_distance(self, point: Point, edge_start: Point, edge_end: Point) -> Tuple[float, Point]:
        """计算点到线段的距离"""
        # 向量计算
        edge_vec = Point(edge_end.x - edge_start.x, edge_end.y - edge_start.y)
        point_vec = Point(point.x - edge_start.x, point.y - edge_start.y)

        edge_len_sq = edge_vec.x**2 + edge_vec.y**2
        if edge_len_sq < self.tolerance:
            return point.distance_to(edge_start), edge_start

        # 投影参数
        t = max(0, min(1, (point_vec.x * edge_vec.x + point_vec.y * edge_vec.y) / edge_len_sq))

        # 最近点
        closest_point = Point(
            edge_start.x + t * edge_vec.x,
            edge_start.y + t * edge_vec.y
        )

        return point.distance_to(closest_point), closest_point

    def _cross_product(self, v1: Point, v2: Point) -> float:
        """计算二维向量叉积"""
        return v1.x * v2.y - v1.y * v2.x

    def _brute_force_distance(self, hull1: List[Point], hull2: List[Point]) -> Dict:
        """暴力搜索最小距离（用于小凸包）"""
        min_distance = float('inf')
        closest_point1 = None
        closest_point2 = None
        contact_type = "separated"

        # 点到点距离
        for p1 in hull1:
            for p2 in hull2:
                dist = p1.distance_to(p2)
                if dist < min_distance:
                    min_distance = dist
                    closest_point1 = p1
                    closest_point2 = p2
                    contact_type = "vertex-vertex"

        # 点到边距离
        for p1 in hull1:
            for i in range(len(hull2)):
                edge_start = hull2[i]
                edge_end = hull2[(i + 1) % len(hull2)]
                dist, closest_on_edge = self._point_to_edge_distance(p1, edge_start, edge_end)
                if dist < min_distance:
                    min_distance = dist
                    closest_point1 = p1
                    closest_point2 = closest_on_edge
                    contact_type = "vertex-edge"

        for p2 in hull2:
            for i in range(len(hull1)):
                edge_start = hull1[i]
                edge_end = hull1[(i + 1) % len(hull1)]
                dist, closest_on_edge = self._point_to_edge_distance(p2, edge_start, edge_end)
                if dist < min_distance:
                    min_distance = dist
                    closest_point1 = closest_on_edge
                    closest_point2 = p2
                    contact_type = "vertex-edge"

        separation_vector = None
        if closest_point1 and closest_point2:
            separation_vector = closest_point2 - closest_point1

        return {
            'min_distance': min_distance,
            'closest_points': {'ship1': closest_point1, 'ship2': closest_point2},
            'contact_type': contact_type,
            'separation_vector': separation_vector,
            'caliper_angle': 0.0,
            'iterations': 1
        }

    def _check_edge_distances(self, hull1: List[Point], hull2: List[Point], i: int, j: int) -> List[Dict]:
        """检查当前点到对方边的距离"""
        edge_distances = []

        # hull1[i] 到 hull2 的边
        for k in range(len(hull2)):
            edge_start = hull2[k]
            edge_end = hull2[(k + 1) % len(hull2)]
            dist, closest_on_edge = self._point_to_edge_distance(hull1[i], edge_start, edge_end)
            edge_distances.append({
                'distance': dist,
                'point1': hull1[i],
                'point2': closest_on_edge,
                'type': 'vertex-edge'
            })

        # hull2[j] 到 hull1 的边
        for k in range(len(hull1)):
            edge_start = hull1[k]
            edge_end = hull1[(k + 1) % len(hull1)]
            dist, closest_on_edge = self._point_to_edge_distance(hull2[j], edge_start, edge_end)
            edge_distances.append({
                'distance': dist,
                'point1': closest_on_edge,
                'point2': hull2[j],
                'type': 'vertex-edge'
            })

        return edge_distances

    def _vector_angle(self, v1: Point, v2: Point) -> float:
        """计算两个向量之间的夹角"""
        dot_product = v1.x * v2.x + v1.y * v2.y
        mag1 = math.sqrt(v1.x**2 + v1.y**2)
        mag2 = math.sqrt(v2.x**2 + v2.y**2)

        if mag1 < self.tolerance or mag2 < self.tolerance:
            return 0.0

        cos_angle = dot_product / (mag1 * mag2)
        cos_angle = max(-1.0, min(1.0, cos_angle))  # 防止数值误差

        return math.acos(cos_angle)

    def _empty_result(self) -> Dict:
        """返回空结果"""
        return {
            'min_distance': float('inf'),
            'closest_points': {'ship1': None, 'ship2': None},
            'contact_type': 'no_data',
            'separation_vector': None,
            'caliper_angle': 0.0,
            'iterations': 0
        }
