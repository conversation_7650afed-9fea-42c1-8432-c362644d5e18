"""
Multi-Ship Coordination System
多船协同避碰系统 - 基于旋转卡壳算法的复杂水域协同导航
"""

import math
from typing import List, Dict, Optional
from dataclasses import dataclass
from enum import Enum

from ..geometry.rotating_calipers import Point, ShipGeometry, RotatingCalipers

class ShipType(Enum):
    """船舶类型"""
    CHANNEL_BOUND = "CHANNEL_BOUND"      # 只能在航道航行的船舶
    CROSSING = "CROSSING"                # 穿越航道的船舶
    FREE_NAVIGATION = "FREE_NAVIGATION"  # 自由航行船舶
    ENTERING_CHANNEL = "ENTERING_CHANNEL" # 进入航道的船舶

class NavigationArea(Enum):
    """航行区域"""
    MAIN_CHANNEL = "MAIN_CHANNEL"        # 主航道
    CROSSING_AREA = "CROSSING_AREA"      # 穿越区域
    ALERT_ZONE = "ALERT_ZONE"           # 警戒区
    FREE_WATER = "FREE_WATER"           # 自由水域

class COLREGsRule(Enum):
    """国际海上避碰规则"""
    RULE_9_NARROW_CHANNEL = "RULE_9"     # 规则9：狭水道
    RULE_13_OVERTAKING = "RULE_13"       # 规则13：追越
    RULE_14_HEAD_ON = "RULE_14"          # 规则14：对遇
    RULE_15_CROSSING = "RULE_15"         # 规则15：交叉相遇

@dataclass
class ShipState:
    """船舶状态"""
    mmsi: int
    geometry: ShipGeometry
    velocity: Point
    ship_type: ShipType
    navigation_area: NavigationArea
    priority_level: int  # 优先级：1最高，数字越大优先级越低
    colregs_status: str  # COLREGs状态：give_way, stand_on, restricted

@dataclass
class ChannelGeometry:
    """航道几何"""
    centerline: List[Point]
    width: float
    boundaries: List[Point]
    crossing_points: List[Point]

@dataclass
class CoordinationConstraints:
    """协同约束"""
    min_separation_distance: float      # 最小分离距离
    channel_priority_margin: float      # 航道优先边距
    crossing_safety_margin: float       # 穿越安全边距
    alert_zone_radius: float            # 警戒区半径
    coordination_horizon: float         # 协同时间范围

class MultiShipCoordinator:
    """多船协同避碰协调器"""
    
    def __init__(self, channel_geometry: ChannelGeometry, constraints: CoordinationConstraints):
        self.channel_geometry = channel_geometry
        self.constraints = constraints
        self.calipers = RotatingCalipers()
        
    def coordinate_multi_ship_avoidance(self, ship_states: List[ShipState]) -> Dict:
        """
        协调多船避碰
        
        Args:
            ship_states: 所有船舶状态列表
            
        Returns:
            Dict: 协同避碰方案
        """
        # 1. 分析船舶间几何关系
        geometric_analysis = self._analyze_multi_ship_geometry(ship_states)
        
        # 2. 应用COLREGs规则确定优先级
        colregs_analysis = self._apply_colregs_rules(ship_states, geometric_analysis)
        
        # 3. 识别协同群组
        coordination_groups = self._identify_coordination_groups(ship_states, geometric_analysis)
        
        # 4. 生成协同避碰策略
        coordination_strategies = self._generate_coordination_strategies(
            ship_states, coordination_groups, colregs_analysis
        )
        
        return {
            'geometric_analysis': geometric_analysis,
            'colregs_analysis': colregs_analysis,
            'coordination_groups': coordination_groups,
            'coordination_strategies': coordination_strategies
        }
    
    def _analyze_multi_ship_geometry(self, ship_states: List[ShipState]) -> Dict:
        """分析多船几何关系"""
        analysis = {
            'pairwise_analysis': {},
            'channel_relationships': {},
            'crossing_conflicts': [],
            'alert_zone_ships': []
        }
        
        # 成对分析所有船舶
        for i, ship1 in enumerate(ship_states):
            for j, ship2 in enumerate(ship_states[i+1:], i+1):
                pair_key = f"{ship1.mmsi}-{ship2.mmsi}"
                
                # 使用旋转卡壳算法计算精确几何关系
                distance_result = self.calipers.rotating_calipers_distance(
                    ship1.geometry.vertices, ship2.geometry.vertices
                )
                
                # 分析相对运动
                relative_motion = self._calculate_relative_motion(ship1, ship2)
                
                # 预测碰撞轨迹
                collision_prediction = self.calipers.predict_collision_time(
                    ship1.geometry, ship1.velocity,
                    ship2.geometry, ship2.velocity,
                    time_horizon=self.constraints.coordination_horizon
                )
                
                # 分析航道关系
                channel_relationship = self._analyze_channel_relationship(ship1, ship2)
                
                analysis['pairwise_analysis'][pair_key] = {
                    'ship1': ship1,
                    'ship2': ship2,
                    'distance_result': distance_result,
                    'relative_motion': relative_motion,
                    'collision_prediction': collision_prediction,
                    'channel_relationship': channel_relationship,
                    'encounter_type': self._classify_encounter_type(ship1, ship2, relative_motion)
                }
        
        # 分析航道关系
        analysis['channel_relationships'] = self._analyze_channel_relationships(ship_states)
        
        # 识别穿越冲突
        analysis['crossing_conflicts'] = self._identify_crossing_conflicts(ship_states)
        
        # 识别警戒区船舶
        analysis['alert_zone_ships'] = self._identify_alert_zone_ships(ship_states)
        
        return analysis
    
    def _apply_colregs_rules(self, ship_states: List[ShipState], geometric_analysis: Dict) -> Dict:
        """应用COLREGs规则"""
        colregs_analysis = {
            'rule_applications': {},
            'priority_matrix': {},
            'give_way_relationships': [],
            'stand_on_vessels': []
        }
        
        for pair_key, pair_analysis in geometric_analysis['pairwise_analysis'].items():
            ship1 = pair_analysis['ship1']
            ship2 = pair_analysis['ship2']
            encounter_type = pair_analysis['encounter_type']
            channel_relationship = pair_analysis['channel_relationship']
            
            # 应用规则9：狭水道
            if self._is_narrow_channel_situation(ship1, ship2):
                rule_result = self._apply_rule_9_narrow_channel(ship1, ship2)
                colregs_analysis['rule_applications'][pair_key] = {
                    'rule': COLREGsRule.RULE_9_NARROW_CHANNEL,
                    'result': rule_result
                }
            
            # 应用规则15：交叉相遇
            elif encounter_type == 'CROSSING':
                rule_result = self._apply_rule_15_crossing(ship1, ship2, pair_analysis)
                colregs_analysis['rule_applications'][pair_key] = {
                    'rule': COLREGsRule.RULE_15_CROSSING,
                    'result': rule_result
                }
            
            # 应用规则14：对遇
            elif encounter_type == 'HEAD_ON':
                rule_result = self._apply_rule_14_head_on(ship1, ship2)
                colregs_analysis['rule_applications'][pair_key] = {
                    'rule': COLREGsRule.RULE_14_HEAD_ON,
                    'result': rule_result
                }
        
        return colregs_analysis
    
    def _identify_coordination_groups(self, ship_states: List[ShipState], 
                                    geometric_analysis: Dict) -> Dict:
        """识别协同群组"""
        groups = {}
        group_id = 0
        processed_ships = set()
        
        for ship_state in ship_states:
            if ship_state.mmsi in processed_ships:
                continue
            
            # 找到需要协同的船舶群组
            group_ships = self._find_coordination_group(ship_state, ship_states, geometric_analysis)
            
            if len(group_ships) > 1:
                groups[group_id] = {
                    'ships': group_ships,
                    'coordination_type': self._determine_coordination_type(group_ships),
                    'leader': self._select_group_leader(group_ships)
                }
                
                for ship in group_ships:
                    processed_ships.add(ship.mmsi)
                
                group_id += 1
        
        return groups
    
    def _generate_coordination_strategies(self, ship_states: List[ShipState],
                                        coordination_groups: Dict, 
                                        colregs_analysis: Dict) -> Dict:
        """生成协同策略"""
        strategies = {}
        
        for group_id, group_info in coordination_groups.items():
            group_ships = group_info['ships']
            coordination_type = group_info['coordination_type']
            leader = group_info['leader']
            
            if coordination_type == 'CHANNEL_CROSSING':
                strategy = self._generate_channel_crossing_strategy(group_ships, leader)
            elif coordination_type == 'ALERT_ZONE_COORDINATION':
                strategy = self._generate_alert_zone_strategy(group_ships, leader)
            else:
                strategy = self._generate_default_coordination_strategy(group_ships, leader)
            
            strategies[group_id] = strategy
        
        return strategies
    
    # 辅助方法实现
    def _calculate_relative_motion(self, ship1: ShipState, ship2: ShipState) -> Dict:
        """计算相对运动"""
        relative_velocity = Point(
            ship2.velocity.x - ship1.velocity.x,
            ship2.velocity.y - ship1.velocity.y
        )
        
        relative_speed = math.sqrt(relative_velocity.x**2 + relative_velocity.y**2)
        
        # 计算相对方位
        dx = ship2.geometry.center.x - ship1.geometry.center.x
        dy = ship2.geometry.center.y - ship1.geometry.center.y
        relative_bearing = math.degrees(math.atan2(dy, dx))
        relative_bearing = (90 - relative_bearing) % 360  # 转换为航海方位
        
        return {
            'relative_velocity': relative_velocity,
            'relative_speed': relative_speed,
            'relative_bearing': relative_bearing,
            'approach_rate': self._calculate_approach_rate(ship1, ship2)
        }
    
    def _calculate_approach_rate(self, ship1: ShipState, ship2: ShipState) -> float:
        """计算接近率"""
        dx = ship2.geometry.center.x - ship1.geometry.center.x
        dy = ship2.geometry.center.y - ship1.geometry.center.y
        current_distance = math.sqrt(dx**2 + dy**2)
        
        relative_velocity = Point(
            ship2.velocity.x - ship1.velocity.x,
            ship2.velocity.y - ship1.velocity.y
        )
        
        if current_distance > 0:
            unit_dx = dx / current_distance
            unit_dy = dy / current_distance
            approach_rate = -(relative_velocity.x * unit_dx + relative_velocity.y * unit_dy)
            return approach_rate
        
        return 0.0
    
    def _classify_encounter_type(self, ship1: ShipState, ship2: ShipState, 
                               relative_motion: Dict) -> str:
        """分类相遇类型"""
        relative_bearing = relative_motion['relative_bearing']
        approach_rate = relative_motion['approach_rate']
        
        if approach_rate < 0:  # 分离
            return 'SEPARATING'
        elif abs(relative_bearing - 180) < 22.5 or abs(relative_bearing) < 22.5:
            return 'HEAD_ON'
        elif 22.5 <= relative_bearing <= 157.5 or 202.5 <= relative_bearing <= 337.5:
            return 'CROSSING'
        elif 157.5 < relative_bearing < 202.5:
            return 'OVERTAKING'
        else:
            return 'COMPLEX'
    
    def _is_narrow_channel_situation(self, ship1: ShipState, ship2: ShipState) -> bool:
        """判断是否为狭水道情况"""
        return (ship1.navigation_area == NavigationArea.MAIN_CHANNEL or 
                ship2.navigation_area == NavigationArea.MAIN_CHANNEL or
                ship1.ship_type == ShipType.CROSSING or 
                ship2.ship_type == ShipType.CROSSING)
    
    def _apply_rule_9_narrow_channel(self, ship1: ShipState, ship2: ShipState) -> Dict:
        """应用COLREGs规则9：狭水道"""
        result = {
            'applicable': True,
            'give_way_vessel': None,
            'stand_on_vessel': None,
            'specific_requirements': []
        }
        
        # 规则9(b)：穿越船舶不应妨碍只能在航道航行的船舶
        if ship1.ship_type == ShipType.CROSSING and ship2.ship_type == ShipType.CHANNEL_BOUND:
            result['give_way_vessel'] = ship1.mmsi
            result['stand_on_vessel'] = ship2.mmsi
            result['specific_requirements'].append("Crossing vessel shall not impede channel-bound vessel")
        elif ship2.ship_type == ShipType.CROSSING and ship1.ship_type == ShipType.CHANNEL_BOUND:
            result['give_way_vessel'] = ship2.mmsi
            result['stand_on_vessel'] = ship1.mmsi
            result['specific_requirements'].append("Crossing vessel shall not impede channel-bound vessel")
        
        return result
    
    def _apply_rule_15_crossing(self, ship1: ShipState, ship2: ShipState, 
                              pair_analysis: Dict) -> Dict:
        """应用COLREGs规则15：交叉相遇"""
        result = {
            'applicable': True,
            'give_way_vessel': None,
            'stand_on_vessel': None,
            'specific_requirements': []
        }
        
        # 计算相对方位
        relative_motion = pair_analysis['relative_motion']
        ship1_bearing_to_ship2 = relative_motion['relative_bearing']
        ship2_bearing_to_ship1 = (ship1_bearing_to_ship2 + 180) % 360
        
        # 规则15：右舷来船有路权
        if 5 <= ship1_bearing_to_ship2 <= 112.5:  # ship2在ship1右舷
            result['give_way_vessel'] = ship1.mmsi
            result['stand_on_vessel'] = ship2.mmsi
            result['specific_requirements'].append("Give way to vessel on starboard side")
        elif 5 <= ship2_bearing_to_ship1 <= 112.5:  # ship1在ship2右舷
            result['give_way_vessel'] = ship2.mmsi
            result['stand_on_vessel'] = ship1.mmsi
            result['specific_requirements'].append("Give way to vessel on starboard side")
        
        return result
    
    def _apply_rule_14_head_on(self, ship1: ShipState, ship2: ShipState) -> Dict:
        """应用COLREGs规则14：对遇"""
        return {
            'applicable': True,
            'give_way_vessel': None,  # 双方都应右转
            'stand_on_vessel': None,
            'specific_requirements': ['Both vessels shall alter course to starboard']
        }
    
    # 其他辅助方法的简化实现
    def _analyze_channel_relationship(self, ship1: ShipState, ship2: ShipState) -> Dict:
        return {'crossing_angle': 90.0, 'crossing_distance': 1.0}
    
    def _analyze_channel_relationships(self, ship_states: List[ShipState]) -> Dict:
        return {'channel_ships': [], 'crossing_ships': []}
    
    def _identify_crossing_conflicts(self, ship_states: List[ShipState]) -> List[Dict]:
        return []
    
    def _identify_alert_zone_ships(self, ship_states: List[ShipState]) -> List[int]:
        return []
    
    def _find_coordination_group(self, target_ship: ShipState, all_ships: List[ShipState],
                               geometric_analysis: Dict) -> List[ShipState]:
        return [target_ship]
    
    def _determine_coordination_type(self, group_ships: List[ShipState]) -> str:
        ship_types = [ship.ship_type for ship in group_ships]
        if ShipType.CROSSING in ship_types and ShipType.CHANNEL_BOUND in ship_types:
            return 'CHANNEL_CROSSING'
        return 'DEFAULT_COORDINATION'
    
    def _select_group_leader(self, group_ships: List[ShipState]) -> ShipState:
        return min(group_ships, key=lambda ship: ship.priority_level)
    
    def _generate_channel_crossing_strategy(self, group_ships: List[ShipState], 
                                          leader: ShipState) -> Dict:
        return {
            'strategy_type': 'CHANNEL_CROSSING',
            'leader': leader.mmsi,
            'coordination_actions': {
                ship.mmsi: {'action_type': 'COORDINATE_CROSSING'} 
                for ship in group_ships
            }
        }
    
    def _generate_alert_zone_strategy(self, group_ships: List[ShipState], 
                                    leader: ShipState) -> Dict:
        return {
            'strategy_type': 'ALERT_ZONE_COORDINATION',
            'leader': leader.mmsi,
            'coordination_actions': {
                ship.mmsi: {'action_type': 'COORDINATE_ENTRY'} 
                for ship in group_ships
            }
        }
    
    def _generate_default_coordination_strategy(self, group_ships: List[ShipState], 
                                              leader: ShipState) -> Dict:
        return {
            'strategy_type': 'DEFAULT_COORDINATION',
            'leader': leader.mmsi,
            'coordination_actions': {
                ship.mmsi: {'action_type': 'MAINTAIN_SAFE_DISTANCE'} 
                for ship in group_ships
            }
        }
