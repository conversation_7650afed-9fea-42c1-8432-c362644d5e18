"""
Real-Time Dynamic Avoidance System
真正的实时动态避让系统

特点：
1. 船舶真正实时运动 - 每帧更新位置
2. 动态避让效果 - 实时航向和速度调整
3. 旋转卡尺实时计算 - 每帧执行碰撞检测
4. 可视化动态效果 - 船舶轨迹、避让动作、风险指示
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Polygon, Circle, Arrow
import math
import time
import warnings
from typing import List, Dict, Tuple, Optional

warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'

class Point:
    """点类"""
    def __init__(self, x: float, y: float):
        self.x = x
        self.y = y

    def distance_to(self, other: 'Point') -> float:
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)

    def __add__(self, other: 'Point') -> 'Point':
        return Point(self.x + other.x, self.y + other.y)

    def __sub__(self, other: 'Point') -> 'Point':
        return Point(self.x - other.x, self.y - other.y)

class DynamicVessel:
    """动态船舶类 - 真正的实时运动"""

    def __init__(self, vessel_id: int, name: str, x: float, y: float,
                 heading: float, speed_knots: float, length: float, width: float,
                 vessel_type: str, color: str):
        # 基本信息
        self.vessel_id = vessel_id
        self.name = name
        self.vessel_type = vessel_type
        self.color = color
        self.length = length
        self.width = width

        # 运动状态
        self.position = Point(x, y)
        self.heading = heading  # 度数，北=0°，东=90°
        self.speed_knots = speed_knots
        self.angular_velocity = 0.0  # 度/秒

        # 计算速度分量（度/秒）
        speed_deg_per_sec = speed_knots * 0.514444 / 111000  # 转换为度/秒
        heading_rad = math.radians(90 - heading)  # 转换为数学角度
        self.velocity = Point(
            speed_deg_per_sec * math.cos(heading_rad),
            speed_deg_per_sec * math.sin(heading_rad)
        )

        # 避让状态
        self.is_avoiding = False
        self.avoidance_start_time = None
        self.original_heading = heading
        self.original_speed = speed_knots
        self.target_heading = heading
        self.target_speed = speed_knots

        # 历史轨迹
        self.trajectory = [Point(x, y)]
        self.max_trajectory_length = 50

        # 风险状态
        self.collision_risks = {}
        self.closest_distances = {}

        print(f"🚢 Created {name}: {vessel_type}, {length}m×{width}m, {speed_knots}kts, heading {heading}°")

    def update_position(self, dt: float):
        """更新船舶位置 - 真正的实时运动"""
        # 更新航向（如果有角速度）
        if abs(self.angular_velocity) > 0.1:
            self.heading += self.angular_velocity * dt
            self.heading = self.heading % 360

            # 更新速度方向
            speed_deg_per_sec = self.speed_knots * 0.514444 / 111000
            heading_rad = math.radians(90 - self.heading)
            self.velocity = Point(
                speed_deg_per_sec * math.cos(heading_rad),
                speed_deg_per_sec * math.sin(heading_rad)
            )

        # 更新位置
        self.position.x += self.velocity.x * dt
        self.position.y += self.velocity.y * dt

        # 记录轨迹
        self.trajectory.append(Point(self.position.x, self.position.y))
        if len(self.trajectory) > self.max_trajectory_length:
            self.trajectory = self.trajectory[-self.max_trajectory_length:]

    def apply_avoidance_maneuver(self, heading_change: float, speed_change: float, current_time: float):
        """应用避让机动 - 动态效果"""
        if not self.is_avoiding:
            self.is_avoiding = True
            self.avoidance_start_time = current_time

            # 设置目标航向和速度
            self.target_heading = (self.heading + heading_change) % 360
            self.target_speed = max(2.0, self.speed_knots * (1 + speed_change))

            # 设置角速度实现平滑转向
            heading_diff = self.target_heading - self.heading
            if heading_diff > 180:
                heading_diff -= 360
            elif heading_diff < -180:
                heading_diff += 360

            # 设置转向速度（度/秒）
            self.angular_velocity = heading_diff / 10.0  # 10秒内完成转向

            print(f"🚨 {self.name} 开始避让: 目标航向 {self.target_heading:.1f}°, "
                  f"目标速度 {self.target_speed:.1f}节")

    def update_avoidance_progress(self, current_time: float):
        """更新避让进度"""
        if self.is_avoiding and self.avoidance_start_time:
            elapsed_time = current_time - self.avoidance_start_time

            # 平滑调整速度
            if abs(self.speed_knots - self.target_speed) > 0.1:
                speed_diff = self.target_speed - self.speed_knots
                self.speed_knots += speed_diff * 0.1  # 平滑调整

                # 更新速度向量
                speed_deg_per_sec = self.speed_knots * 0.514444 / 111000
                heading_rad = math.radians(90 - self.heading)
                self.velocity = Point(
                    speed_deg_per_sec * math.cos(heading_rad),
                    speed_deg_per_sec * math.sin(heading_rad)
                )

            # 检查是否完成避让
            if (abs(self.heading - self.target_heading) < 1.0 and
                abs(self.speed_knots - self.target_speed) < 0.1 and
                elapsed_time > 15.0):  # 至少避让15秒

                self.angular_velocity = 0.0
                print(f"✅ {self.name} 完成避让机动")

    def get_vertices(self) -> List[Point]:
        """获取船舶顶点"""
        # 船舶尺寸转换
        half_length = (self.length / 111000) / 2
        half_width = (self.width / 111000) / 2

        # 船舶轮廓点（相对坐标）
        relative_points = [
            Point(half_length, 0),                    # 船首
            Point(half_length * 0.7, half_width),     # 右前
            Point(-half_length * 0.3, half_width),    # 右中
            Point(-half_length, half_width * 0.6),    # 右后
            Point(-half_length, -half_width * 0.6),   # 左后
            Point(-half_length * 0.3, -half_width),   # 左中
            Point(half_length * 0.7, -half_width),    # 左前
        ]

        # 旋转到当前航向
        heading_rad = math.radians(90 - self.heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        vertices = []
        for point in relative_points:
            rotated_x = point.x * cos_h - point.y * sin_h
            rotated_y = point.x * sin_h + point.y * cos_h
            vertices.append(Point(
                self.position.x + rotated_x,
                self.position.y + rotated_y
            ))

        return vertices

class SimpleRotatingCalipers:
    """简化的旋转卡尺算法"""

    def calculate_distance(self, vertices1: List[Point], vertices2: List[Point]) -> Dict:
        """计算两个凸包之间的最小距离"""
        min_distance = float('inf')
        closest_point1 = None
        closest_point2 = None

        # 简化算法：检查所有顶点对
        for v1 in vertices1:
            for v2 in vertices2:
                distance = v1.distance_to(v2)
                if distance < min_distance:
                    min_distance = distance
                    closest_point1 = v1
                    closest_point2 = v2

        return {
            'min_distance': min_distance,
            'closest_points': {
                'point1': closest_point1,
                'point2': closest_point2
            }
        }

class RealTimeDynamicAvoidanceSystem:
    """真正的实时动态避让系统"""

    def __init__(self):
        # 创建图形
        self.fig, self.ax = plt.subplots(figsize=(16, 12))
        self.setup_environment()

        # 船舶和算法
        self.vessels = []
        self.calipers = SimpleRotatingCalipers()

        # 仿真参数
        self.current_time = 0.0
        self.dt = 1.0  # 1秒时间步长
        self.frame_count = 0

        # 可视化元素
        self.vessel_patches = {}
        self.trajectory_lines = {}
        self.risk_lines = []
        self.info_text = None

        # 安全阈值
        self.safety_thresholds = {
            'emergency': 0.0008,   # 约0.05海里
            'critical': 0.0015,    # 约0.09海里
            'warning': 0.003,      # 约0.18海里
        }

        # 创建船舶
        self.create_dynamic_scenario()

        print(f"\n🚀 Real-Time Dynamic Avoidance System Initialized")
        print(f"   Vessels: {len(self.vessels)}")
        print(f"   Time step: {self.dt}s")
        print(f"   Safety thresholds: {self.safety_thresholds}")

    def setup_environment(self):
        """设置环境"""
        self.ax.set_xlim(120.98, 121.04)
        self.ax.set_ylim(30.98, 31.03)
        self.ax.set_xlabel('Longitude (°E)', fontsize=12)
        self.ax.set_ylabel('Latitude (°N)', fontsize=12)
        self.ax.grid(True, alpha=0.3)
        self.ax.set_aspect('equal')

        # 绘制航道
        from matplotlib.patches import Rectangle
        channel = Rectangle((121.008, 30.985), 0.008, 0.040,
                          linewidth=3, edgecolor='blue', facecolor='lightblue',
                          alpha=0.3, label='Main Channel')
        self.ax.add_patch(channel)

        # 航道中心线
        self.ax.plot([121.012, 121.012], [30.985, 31.025], 'b--',
                    linewidth=2, alpha=0.8, label='Channel Centerline')

        # 汽渡航线
        ferry_routes = [30.995, 31.000, 31.005, 31.010, 31.015]
        for y in ferry_routes:
            self.ax.plot([120.985, 121.035], [y, y], 'r:', linewidth=1, alpha=0.6)

        self.ax.set_title('Real-Time Dynamic Avoidance System\n'
                         'Live Ship Movement with Rotating Calipers',
                         fontsize=14, fontweight='bold')

        # 信息文本
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes,
                                     verticalalignment='top', fontsize=10,
                                     bbox=dict(boxstyle='round,pad=0.5',
                                              facecolor='lightcyan', alpha=0.9))

    def create_dynamic_scenario(self):
        """创建动态场景"""
        vessel_configs = [
            # 主航道船舶
            {
                'vessel_id': 1, 'name': 'COSCO_SHANGHAI', 'x': 121.010, 'y': 30.990,
                'heading': 0.0, 'speed_knots': 15.0, 'length': 400.0, 'width': 58.0,
                'vessel_type': 'Channel', 'color': 'darkblue'
            },
            {
                'vessel_id': 2, 'name': 'SINOPEC_GLORY', 'x': 121.014, 'y': 31.025,
                'heading': 180.0, 'speed_knots': 12.0, 'length': 380.0, 'width': 68.0,
                'vessel_type': 'Channel', 'color': 'navy'
            },

            # 穿越船舶
            {
                'vessel_id': 3, 'name': 'FERRY_EXPRESS', 'x': 120.990, 'y': 31.000,
                'heading': 90.0, 'speed_knots': 18.0, 'length': 180.0, 'width': 28.0,
                'vessel_type': 'Crossing', 'color': 'darkred'
            },
            {
                'vessel_id': 4, 'name': 'CAR_FERRY', 'x': 120.985, 'y': 31.005,
                'heading': 90.0, 'speed_knots': 14.0, 'length': 160.0, 'width': 25.0,
                'vessel_type': 'Crossing', 'color': 'red'
            },
            {
                'vessel_id': 5, 'name': 'PASSENGER_SHIP', 'x': 121.035, 'y': 31.010,
                'heading': 270.0, 'speed_knots': 16.0, 'length': 150.0, 'width': 22.0,
                'vessel_type': 'Crossing', 'color': 'maroon'
            }
        ]

        for config in vessel_configs:
            vessel = DynamicVessel(**config)
            self.vessels.append(vessel)

            # 创建可视化元素
            vertices = vessel.get_vertices()
            vertex_coords = [(v.x, v.y) for v in vertices]

            patch = Polygon(vertex_coords, closed=True,
                           facecolor=vessel.color, edgecolor='black',
                           alpha=0.8, linewidth=2)
            self.ax.add_patch(patch)
            self.vessel_patches[vessel.vessel_id] = patch

            # 轨迹线
            line, = self.ax.plot([], [], color=vessel.color, linewidth=2, alpha=0.6)
            self.trajectory_lines[vessel.vessel_id] = line

    def analyze_collision_risks(self) -> Dict:
        """实时分析碰撞风险"""
        analysis_start = time.time()

        # 清除之前的风险线
        for line in self.risk_lines:
            line.remove()
        self.risk_lines = []

        critical_pairs = []
        total_calculations = 0

        # 对每对船舶执行旋转卡尺分析
        for i in range(len(self.vessels)):
            for j in range(i + 1, len(self.vessels)):
                vessel1 = self.vessels[i]
                vessel2 = self.vessels[j]

                # 获取船舶顶点
                vertices1 = vessel1.get_vertices()
                vertices2 = vessel2.get_vertices()

                # 执行旋转卡尺算法
                result = self.calipers.calculate_distance(vertices1, vertices2)
                total_calculations += 1

                distance = result['min_distance']
                distance_nm = distance * 60  # 转换为海里

                # 更新船舶风险信息
                vessel1.closest_distances[vessel2.vessel_id] = distance_nm
                vessel2.closest_distances[vessel1.vessel_id] = distance_nm

                # 评估风险等级
                risk_level = self.assess_risk_level(distance)

                if risk_level != 'safe':
                    critical_pairs.append({
                        'vessel1': vessel1,
                        'vessel2': vessel2,
                        'distance': distance,
                        'distance_nm': distance_nm,
                        'risk_level': risk_level,
                        'result': result
                    })

                    # 绘制风险指示器
                    self.draw_risk_indicator(vessel1, vessel2, result, risk_level)

        analysis_time = (time.time() - analysis_start) * 1000

        return {
            'critical_pairs': critical_pairs,
            'total_calculations': total_calculations,
            'analysis_time_ms': analysis_time
        }

    def assess_risk_level(self, distance: float) -> str:
        """评估风险等级"""
        if distance < self.safety_thresholds['emergency']:
            return 'emergency'
        elif distance < self.safety_thresholds['critical']:
            return 'critical'
        elif distance < self.safety_thresholds['warning']:
            return 'warning'
        else:
            return 'safe'

    def draw_risk_indicator(self, vessel1: DynamicVessel, vessel2: DynamicVessel,
                           result: Dict, risk_level: str):
        """绘制风险指示器"""
        cp1 = result['closest_points']['point1']
        cp2 = result['closest_points']['point2']

        # 风险颜色
        colors = {
            'emergency': 'red',
            'critical': 'orange',
            'warning': 'yellow'
        }

        color = colors.get(risk_level, 'gray')

        # 绘制距离线
        line, = self.ax.plot([cp1.x, cp2.x], [cp1.y, cp2.y],
                           color=color, linewidth=3, alpha=0.8)
        self.risk_lines.append(line)

        # 标记距离
        mid_x = (cp1.x + cp2.x) / 2
        mid_y = (cp1.y + cp2.y) / 2
        distance_nm = result['min_distance'] * 60

        text = self.ax.annotate(f'{distance_nm:.2f}nm\n{risk_level.upper()}',
                               (mid_x, mid_y),
                               xytext=(0, 0), textcoords='offset points',
                               fontsize=9, ha='center', fontweight='bold',
                               bbox=dict(boxstyle='round,pad=0.3',
                                        facecolor=color, alpha=0.8))
        self.risk_lines.append(text)

    def execute_avoidance_strategies(self, analysis_result: Dict):
        """执行避让策略"""
        for pair in analysis_result['critical_pairs']:
            vessel1 = pair['vessel1']
            vessel2 = pair['vessel2']
            risk_level = pair['risk_level']

            # 根据COLREGs规则确定避让方
            avoiding_vessel, target_vessel = self.determine_give_way_vessel(vessel1, vessel2)

            if avoiding_vessel and not avoiding_vessel.is_avoiding:
                # 生成避让策略
                heading_change, speed_change = self.generate_avoidance_strategy(
                    avoiding_vessel, target_vessel, risk_level
                )

                # 应用避让机动
                avoiding_vessel.apply_avoidance_maneuver(
                    heading_change, speed_change, self.current_time
                )

    def determine_give_way_vessel(self, vessel1: DynamicVessel,
                                 vessel2: DynamicVessel) -> Tuple[Optional[DynamicVessel], Optional[DynamicVessel]]:
        """确定避让方"""
        # COLREGs规则9：穿越船舶避让主航道船舶
        if vessel1.vessel_type == 'Crossing' and vessel2.vessel_type == 'Channel':
            return vessel1, vessel2
        elif vessel2.vessel_type == 'Crossing' and vessel1.vessel_type == 'Channel':
            return vessel2, vessel1

        # 同类型船舶：右舷来船有路权
        elif vessel1.vessel_type == vessel2.vessel_type:
            relative_bearing = self.calculate_relative_bearing(vessel1, vessel2)
            if 5 <= relative_bearing <= 112.5:  # vessel2在vessel1右舷
                return vessel1, vessel2
            else:
                return vessel2, vessel1

        return None, None

    def calculate_relative_bearing(self, vessel1: DynamicVessel, vessel2: DynamicVessel) -> float:
        """计算相对方位"""
        dx = vessel2.position.x - vessel1.position.x
        dy = vessel2.position.y - vessel1.position.y
        bearing = math.degrees(math.atan2(dy, dx))
        relative_bearing = (90 - bearing - vessel1.heading) % 360
        return relative_bearing

    def generate_avoidance_strategy(self, avoiding_vessel: DynamicVessel,
                                   target_vessel: DynamicVessel, risk_level: str) -> Tuple[float, float]:
        """生成避让策略"""
        # 根据风险等级确定避让强度
        if risk_level == 'emergency':
            heading_change = 45.0
            speed_change = -0.6  # 减速60%
        elif risk_level == 'critical':
            heading_change = 30.0
            speed_change = -0.4  # 减速40%
        else:  # warning
            heading_change = 20.0
            speed_change = -0.2  # 减速20%

        # 根据船舶类型调整避让方向
        if avoiding_vessel.vessel_type == 'Crossing':
            # 穿越船舶：向右转
            if avoiding_vessel.heading == 90.0:  # 东向
                heading_change = +abs(heading_change)  # 右转（向南）
            elif avoiding_vessel.heading == 270.0:  # 西向
                heading_change = -abs(heading_change)  # 右转（向北）

        return heading_change, speed_change

    def update_visualization(self):
        """更新可视化 - 实时动态效果"""
        for vessel in self.vessels:
            # 更新船舶多边形
            vertices = vessel.get_vertices()
            vertex_coords = [(v.x, v.y) for v in vertices]
            self.vessel_patches[vessel.vessel_id].set_xy(vertex_coords)

            # 根据避让状态调整颜色
            if vessel.is_avoiding:
                self.vessel_patches[vessel.vessel_id].set_facecolor('orange')
                self.vessel_patches[vessel.vessel_id].set_alpha(1.0)
            else:
                self.vessel_patches[vessel.vessel_id].set_facecolor(vessel.color)
                self.vessel_patches[vessel.vessel_id].set_alpha(0.8)

            # 更新轨迹线
            if len(vessel.trajectory) > 1:
                traj_x = [p.x for p in vessel.trajectory]
                traj_y = [p.y for p in vessel.trajectory]
                self.trajectory_lines[vessel.vessel_id].set_data(traj_x, traj_y)

    def update_info_display(self, analysis_result: Dict):
        """更新信息显示"""
        avoiding_count = sum(1 for v in self.vessels if v.is_avoiding)

        info_text = f"""REAL-TIME DYNAMIC AVOIDANCE SYSTEM

Time: {self.current_time:.1f}s ({self.current_time/60:.1f}min)
Vessels: {len(self.vessels)} | Avoiding: {avoiding_count}

REAL-TIME ANALYSIS:
Critical Pairs: {len(analysis_result['critical_pairs'])}
Calculations: {analysis_result['total_calculations']}
Analysis Time: {analysis_result['analysis_time_ms']:.1f}ms

LIVE FEATURES:
✓ Real-time ship movement
✓ Dynamic collision detection
✓ Automatic avoidance maneuvers
✓ COLREGs rules enforcement
✓ Live trajectory tracking

VESSEL STATUS:"""

        for vessel in self.vessels:
            status = "AVOIDING" if vessel.is_avoiding else "NORMAL"
            info_text += f"\n{vessel.name}: {status}"
            if vessel.is_avoiding:
                info_text += f" (→{vessel.target_heading:.0f}°)"

        self.info_text.set_text(info_text)

    def update_frame(self, frame):
        """更新动画帧 - 核心实时动态函数"""
        self.frame_count = frame
        self.current_time = frame * self.dt

        # 1. 更新所有船舶位置 - 真正的实时运动
        for vessel in self.vessels:
            vessel.update_position(self.dt)
            vessel.update_avoidance_progress(self.current_time)

        # 2. 实时分析碰撞风险
        analysis_result = self.analyze_collision_risks()

        # 3. 执行避让策略
        self.execute_avoidance_strategies(analysis_result)

        # 4. 更新可视化
        self.update_visualization()

        # 5. 更新信息显示
        self.update_info_display(analysis_result)

        # 返回需要更新的艺术家对象
        return (list(self.vessel_patches.values()) +
                list(self.trajectory_lines.values()) +
                [self.info_text])

    def create_animation(self, frames=300, interval=1000):
        """创建实时动画"""
        print(f"\n🎬 Creating Real-Time Dynamic Animation...")
        print(f"   Frames: {frames}")
        print(f"   Interval: {interval}ms")
        print(f"   Real simulation time: {frames * self.dt / 60:.1f} minutes")

        anim = animation.FuncAnimation(
            self.fig, self.update_frame, frames=frames,
            interval=interval, blit=False, repeat=False
        )

        return anim

    def run_simulation(self, save_animation=True, show_plot=True):
        """运行实时仿真"""
        print(f"\n🚀 Starting Real-Time Dynamic Avoidance Simulation")
        print("="*70)

        # 创建动画
        anim = self.create_animation(frames=300, interval=1000)  # 5分钟仿真

        if save_animation:
            try:
                print(f"\n💾 Saving real-time animation...")
                anim.save('real_time_dynamic_avoidance.gif',
                         writer='pillow', fps=1, dpi=120)
                print(f"✅ Animation saved as 'real_time_dynamic_avoidance.gif'")
            except Exception as e:
                print(f"❌ Failed to save animation: {e}")

        if show_plot:
            plt.tight_layout()
            plt.show()

        return anim

def main():
    """主函数"""
    print("🌊 Real-Time Dynamic Avoidance System")
    print("="*60)
    print("True Real-Time Ship Movement with Dynamic Collision Avoidance")

    try:
        # 创建并运行系统
        system = RealTimeDynamicAvoidanceSystem()
        anim = system.run_simulation(save_animation=True, show_plot=True)

        print(f"\n🎉 REAL-TIME SIMULATION COMPLETED!")
        print(f"✅ Features Successfully Demonstrated:")
        print(f"   🚢 True real-time ship movement")
        print(f"   🔄 Dynamic collision detection every frame")
        print(f"   🚨 Automatic avoidance maneuvers")
        print(f"   ⚖️ COLREGs rules enforcement")
        print(f"   🎬 Live trajectory visualization")
        print(f"   📊 Real-time performance monitoring")

    except Exception as e:
        print(f"❌ Simulation failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
