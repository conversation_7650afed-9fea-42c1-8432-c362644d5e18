"""
Complex Ferry Crossing Scenario
复杂汽渡穿越场景 - 南北主航道与东西汽渡轮的多船协同避碰

场景描述：
- 南北方向：主航道双向航行，享有航行优先权
- 东西方向：多条汽渡轮穿越航道，应避让主航道船舶
- COLREGs规则9：穿越船舶不应妨碍只能在航道航行的船舶
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, Circle, Rectangle, Arrow
import math
import warnings

warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'

from src_clean.geometry.advanced_rotating_calipers import (
    Point, VesselState, DynamicConvexHull, AdvancedRotatingCalipers, CollisionRisk
)
from src_clean.coordination.geometric_collision_avoidance import (
    GeometricCollisionAvoidance, AvoidanceStrategy, ManeuverPriority
)

def create_complex_ferry_crossing_scenario():
    """创建复杂汽渡穿越场景"""
    
    current_time = 1640995200.0  # 基准时间
    vessel_states = []
    
    # ==================== 南北主航道船舶 ====================
    
    # 1. 北向大型集装箱船 - 主航道优先权
    vessel_states.append(VesselState(
        position=Point(121.010, 30.995),
        velocity=Point(0.0, 0.00025),        # 约15节北向
        heading=0.0,                         # 正北
        angular_velocity=0.0,
        length=400.0, width=58.0,            # 超大型集装箱船
        timestamp=current_time
    ))
    
    # 2. 北向散货船 - 主航道优先权
    vessel_states.append(VesselState(
        position=Point(121.010, 31.000),
        velocity=Point(0.0, 0.0002),         # 约12节北向
        heading=0.0,                         # 正北
        angular_velocity=0.0,
        length=280.0, width=45.0,            # 散货船
        timestamp=current_time
    ))
    
    # 3. 南向油轮 - 主航道优先权
    vessel_states.append(VesselState(
        position=Point(121.012, 31.020),
        velocity=Point(0.0, -0.00018),       # 约11节南向
        heading=180.0,                       # 正南
        angular_velocity=0.0,
        length=350.0, width=60.0,            # 大型油轮
        timestamp=current_time
    ))
    
    # 4. 南向货船 - 主航道优先权
    vessel_states.append(VesselState(
        position=Point(121.012, 31.015),
        velocity=Point(0.0, -0.0002),        # 约12节南向
        heading=180.0,                       # 正南
        angular_velocity=0.0,
        length=220.0, width=32.0,            # 货船
        timestamp=current_time
    ))
    
    # ==================== 东西向汽渡轮 ====================
    
    # 5. 东向汽渡轮1 - 需要避让主航道
    vessel_states.append(VesselState(
        position=Point(121.000, 31.008),
        velocity=Point(0.00022, 0.0),        # 约13节东向
        heading=90.0,                        # 正东
        angular_velocity=0.0,
        length=180.0, width=28.0,            # 汽渡轮
        timestamp=current_time
    ))
    
    # 6. 东向汽渡轮2 - 需要避让主航道
    vessel_states.append(VesselState(
        position=Point(120.998, 31.005),
        velocity=Point(0.0002, 0.0),         # 约12节东向
        heading=90.0,                        # 正东
        angular_velocity=0.0,
        length=160.0, width=25.0,            # 汽渡轮
        timestamp=current_time
    ))
    
    # 7. 西向汽渡轮1 - 需要避让主航道
    vessel_states.append(VesselState(
        position=Point(121.025, 31.012),
        velocity=Point(-0.00018, 0.0),       # 约11节西向
        heading=270.0,                       # 正西
        angular_velocity=0.0,
        length=170.0, width=26.0,            # 汽渡轮
        timestamp=current_time
    ))
    
    # 8. 西向汽渡轮2 - 需要避让主航道
    vessel_states.append(VesselState(
        position=Point(121.022, 31.006),
        velocity=Point(-0.0002, 0.0),        # 约12节西向
        heading=270.0,                       # 正西
        angular_velocity=0.0,
        length=150.0, width=24.0,            # 汽渡轮
        timestamp=current_time
    ))
    
    # 9. 东向高速客轮 - 需要避让主航道
    vessel_states.append(VesselState(
        position=Point(120.995, 31.010),
        velocity=Point(0.0003, 0.0),         # 约18节东向
        heading=90.0,                        # 正东
        angular_velocity=0.0,
        length=120.0, width=18.0,            # 高速客轮
        timestamp=current_time
    ))
    
    # 10. 西向高速客轮 - 需要避让主航道
    vessel_states.append(VesselState(
        position=Point(121.028, 31.003),
        velocity=Point(-0.00025, 0.0),       # 约15节西向
        heading=270.0,                       # 正西
        angular_velocity=0.0,
        length=110.0, width=16.0,            # 高速客轮
        timestamp=current_time
    ))
    
    return vessel_states

def analyze_ferry_crossing_scenario():
    """分析汽渡穿越场景"""
    
    print("🚢 Complex Ferry Crossing Scenario Analysis")
    print("="*70)
    print("南北主航道双向航行 vs 东西汽渡轮穿越")
    
    # 创建场景
    vessel_states = create_complex_ferry_crossing_scenario()
    
    # 分类船舶
    ns_vessels = []  # 南北向主航道船舶
    ew_vessels = []  # 东西向穿越船舶
    
    for i, vessel in enumerate(vessel_states):
        if vessel.heading in [0.0, 180.0]:  # 南北向
            ns_vessels.append((i, vessel))
        else:  # 东西向
            ew_vessels.append((i, vessel))
    
    print(f"\n📊 Scenario Composition:")
    print(f"   南北主航道船舶: {len(ns_vessels)} 艘")
    print(f"   东西穿越船舶: {len(ew_vessels)} 艘")
    print(f"   总船舶数: {len(vessel_states)} 艘")
    
    # 显示船舶详情
    print(f"\n🚢 南北主航道船舶 (享有优先权):")
    for i, vessel in ns_vessels:
        direction = "北向" if vessel.heading == 0.0 else "南向"
        speed_knots = vessel.velocity.magnitude() * 111000 / 0.514444
        print(f"   船舶{i+1}: {vessel.length}m×{vessel.width}m, "
              f"{speed_knots:.1f}节, {direction}")
    
    print(f"\n⛴️ 东西穿越船舶 (需要避让):")
    for i, vessel in ew_vessels:
        direction = "东向" if vessel.heading == 90.0 else "西向"
        speed_knots = vessel.velocity.magnitude() * 111000 / 0.514444
        vessel_type = "高速客轮" if vessel.length < 130 else "汽渡轮"
        print(f"   船舶{i+1}: {vessel.length}m×{vessel.width}m, "
              f"{speed_knots:.1f}节, {direction}, {vessel_type}")
    
    # 创建避碰系统
    avoidance_system = GeometricCollisionAvoidance(safety_domain_radius=0.002)
    
    print(f"\n🔄 执行多船协同分析...")
    
    # 执行协同分析
    coordination_result = avoidance_system.execute_dynamic_coordination(
        vessel_states, time_horizon=600.0
    )
    
    if 'error' in coordination_result:
        print(f"❌ 分析失败: {coordination_result['error']}")
        return None
    
    # 分析结果
    geometry_analysis = coordination_result['geometry_analysis']
    avoidance_maneuvers = coordination_result['avoidance_maneuvers']
    
    print(f"\n📏 距离分析结果:")
    envelope_analysis = geometry_analysis['envelope_analysis']
    critical_pairs = []
    
    for pair_key, data in envelope_analysis['minimum_separation_matrix'].items():
        distance_nm = data['distance'] * 60
        risk_level = data['risk_level']
        
        if risk_level in [CollisionRisk.WARNING, CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
            vessel_ids = [int(x.split('_')[1]) for x in pair_key.split('_') if x.isdigit()]
            if len(vessel_ids) == 2:
                v1, v2 = vessel_ids[0], vessel_ids[1]
                v1_type = "主航道" if vessel_states[v1].heading in [0.0, 180.0] else "穿越"
                v2_type = "主航道" if vessel_states[v2].heading in [0.0, 180.0] else "穿越"
                
                print(f"   船舶{v1+1}({v1_type}) ↔ 船舶{v2+1}({v2_type}): "
                      f"{distance_nm:.2f}nm, {risk_level.value}")
                critical_pairs.append((v1, v2, distance_nm, risk_level))
    
    print(f"\n🎯 避让策略分析:")
    ns_maneuvers = 0
    ew_maneuvers = 0
    
    for maneuver in avoidance_maneuvers:
        vessel = vessel_states[maneuver.vessel_id]
        vessel_type = "主航道" if vessel.heading in [0.0, 180.0] else "穿越"
        
        if maneuver.strategy != AvoidanceStrategy.MAINTAIN_COURSE:
            print(f"   船舶{maneuver.vessel_id+1}({vessel_type}): "
                  f"{maneuver.strategy.value}")
            print(f"     航向改变: {maneuver.heading_change:+.1f}°")
            print(f"     速度改变: {maneuver.speed_change:+.1%}")
            print(f"     优先级: {maneuver.priority.value}")
            
            if vessel_type == "主航道":
                ns_maneuvers += 1
            else:
                ew_maneuvers += 1
    
    print(f"\n📊 COLREGs规则9合规性分析:")
    print(f"   主航道船舶需要避让: {ns_maneuvers} 艘")
    print(f"   穿越船舶需要避让: {ew_maneuvers} 艘")
    
    if ns_maneuvers == 0:
        print(f"   ✅ 完全符合COLREGs规则9：穿越船舶不妨碍主航道船舶")
    elif ns_maneuvers < ew_maneuvers:
        print(f"   ⚠️ 基本符合COLREGs规则9：主要由穿越船舶避让")
    else:
        print(f"   ❌ 不符合COLREGs规则9：主航道船舶避让过多")
    
    # 性能评估
    performance = coordination_result['performance_metrics']
    print(f"\n🚀 系统性能:")
    print(f"   安全性提升: {performance['safety_improvement']:.1%}")
    print(f"   协调质量: {performance['coordination_quality']:.1%}")
    print(f"   计算时间: {performance['computational_time']*1000:.1f}ms")
    
    return {
        'vessel_states': vessel_states,
        'coordination_result': coordination_result,
        'ns_vessels': ns_vessels,
        'ew_vessels': ew_vessels,
        'critical_pairs': critical_pairs
    }

def visualize_ferry_crossing_scenario(analysis_result):
    """可视化汽渡穿越场景"""
    
    if not analysis_result:
        return
    
    vessel_states = analysis_result['vessel_states']
    coordination_result = analysis_result['coordination_result']
    ns_vessels = analysis_result['ns_vessels']
    ew_vessels = analysis_result['ew_vessels']
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # 子图1：整体场景
    ax1.set_title('Complex Ferry Crossing Scenario\n南北主航道 vs 东西汽渡穿越', 
                  fontweight='bold', fontsize=12)
    _plot_overall_scenario(ax1, vessel_states, ns_vessels, ew_vessels)
    
    # 子图2：碰撞风险分析
    ax2.set_title('Collision Risk Analysis\n基于旋转卡壳的风险评估', 
                  fontweight='bold', fontsize=12)
    _plot_collision_risks(ax2, vessel_states, coordination_result)
    
    # 子图3：避让策略
    ax3.set_title('Avoidance Strategies\nCOLREGs Rule 9 Compliance', 
                  fontweight='bold', fontsize=12)
    _plot_avoidance_strategies(ax3, vessel_states, coordination_result)
    
    # 子图4：时空预测
    ax4.set_title('Spatio-Temporal Prediction\n时空轨迹预测', 
                  fontweight='bold', fontsize=12)
    _plot_trajectory_prediction(ax4, vessel_states, coordination_result)
    
    plt.tight_layout()
    plt.savefig('complex_ferry_crossing_scenario.png', dpi=300, bbox_inches='tight')
    plt.show()

def _plot_overall_scenario(ax, vessel_states, ns_vessels, ew_vessels):
    """绘制整体场景"""
    
    # 绘制主航道
    channel_rect = Rectangle((121.008, 30.990), 0.006, 0.035,
                            linewidth=3, edgecolor='blue', facecolor='lightblue',
                            alpha=0.3, label='主航道 (南北双向)')
    ax.add_patch(channel_rect)
    
    # 绘制航道中心线
    ax.plot([121.011, 121.011], [30.990, 31.025], 'b--', 
           linewidth=2, alpha=0.8, label='航道中心线')
    
    # 绘制汽渡航线
    ferry_lines_y = [31.003, 31.005, 31.006, 31.008, 31.010, 31.012]
    for y in ferry_lines_y:
        ax.plot([120.995, 121.030], [y, y], 'r:', 
               linewidth=1, alpha=0.6)
    ax.plot([120.995, 121.030], [ferry_lines_y[0], ferry_lines_y[0]], 'r:', 
           linewidth=1, alpha=0.6, label='汽渡航线 (东西穿越)')
    
    # 绘制南北向船舶（主航道）
    for i, vessel in ns_vessels:
        color = 'darkblue' if vessel.heading == 0.0 else 'navy'
        marker = '^' if vessel.heading == 0.0 else 'v'
        
        ax.scatter(vessel.position.x, vessel.position.y, s=300, 
                  color=color, marker=marker, alpha=0.9, zorder=10,
                  edgecolors='white', linewidth=2)
        
        # 标记船舶信息
        direction = "N" if vessel.heading == 0.0 else "S"
        ax.annotate(f'{i+1}{direction}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points',
                   ha='center', va='center', fontweight='bold', 
                   color='white', fontsize=8)
    
    # 绘制东西向船舶（穿越）
    for i, vessel in ew_vessels:
        color = 'darkred' if vessel.heading == 90.0 else 'maroon'
        marker = '>' if vessel.heading == 90.0 else '<'
        
        ax.scatter(vessel.position.x, vessel.position.y, s=200, 
                  color=color, marker=marker, alpha=0.9, zorder=10,
                  edgecolors='white', linewidth=1)
        
        # 标记船舶信息
        direction = "E" if vessel.heading == 90.0 else "W"
        ax.annotate(f'{i+1}{direction}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points',
                   ha='center', va='center', fontweight='bold', 
                   color='white', fontsize=7)
    
    _setup_maritime_plot(ax)
    ax.legend(loc='upper left', fontsize=10)

def _plot_collision_risks(ax, vessel_states, coordination_result):
    """绘制碰撞风险"""
    
    # 绘制所有船舶
    for i, vessel in enumerate(vessel_states):
        if vessel.heading in [0.0, 180.0]:  # 南北向
            color = 'blue'
            marker = 'o'
        else:  # 东西向
            color = 'red'
            marker = 's'
        
        ax.scatter(vessel.position.x, vessel.position.y, s=150, 
                  color=color, marker=marker, alpha=0.8, zorder=10)
        ax.annotate(f'{i+1}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points',
                   ha='center', va='center', fontweight='bold', 
                   color='white', fontsize=8)
    
    # 绘制风险连线
    envelope_analysis = coordination_result['geometry_analysis']['envelope_analysis']
    
    for pair_key, data in envelope_analysis['minimum_separation_matrix'].items():
        distance_nm = data['distance'] * 60
        risk_level = data['risk_level']
        
        if risk_level in [CollisionRisk.WARNING, CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
            vessel_ids = [int(x.split('_')[1]) for x in pair_key.split('_') if x.isdigit()]
            if len(vessel_ids) == 2:
                v1, v2 = vessel_ids[0], vessel_ids[1]
                if v1 < len(vessel_states) and v2 < len(vessel_states):
                    pos1 = vessel_states[v1].position
                    pos2 = vessel_states[v2].position
                    
                    # 根据风险等级设置颜色
                    if risk_level == CollisionRisk.IMMINENT:
                        color = 'red'
                        width = 4
                    elif risk_level == CollisionRisk.CRITICAL:
                        color = 'orange'
                        width = 3
                    else:
                        color = 'yellow'
                        width = 2
                    
                    ax.plot([pos1.x, pos2.x], [pos1.y, pos2.y], 
                           color=color, linewidth=width, alpha=0.8)
                    
                    # 标记距离
                    mid_x = (pos1.x + pos2.x) / 2
                    mid_y = (pos1.y + pos2.y) / 2
                    ax.annotate(f'{distance_nm:.1f}nm', (mid_x, mid_y),
                               fontsize=8, ha='center',
                               bbox=dict(boxstyle='round,pad=0.2', 
                                        facecolor=color, alpha=0.7))
    
    _setup_maritime_plot(ax)

def _plot_avoidance_strategies(ax, vessel_states, coordination_result):
    """绘制避让策略"""
    
    avoidance_maneuvers = coordination_result['avoidance_maneuvers']
    
    for i, (vessel, maneuver) in enumerate(zip(vessel_states, avoidance_maneuvers)):
        # 基础颜色
        if vessel.heading in [0.0, 180.0]:  # 南北向
            base_color = 'blue'
        else:  # 东西向
            base_color = 'red'
        
        # 根据避让策略调整颜色
        if maneuver.strategy == AvoidanceStrategy.MAINTAIN_COURSE:
            color = base_color
            alpha = 0.6
            size = 100
        else:
            color = 'orange' if maneuver.priority.value >= 3 else 'yellow'
            alpha = 0.9
            size = 200
        
        ax.scatter(vessel.position.x, vessel.position.y, s=size, 
                  color=color, alpha=alpha, zorder=10,
                  edgecolors='black', linewidth=1)
        
        # 标记避让信息
        if maneuver.strategy != AvoidanceStrategy.MAINTAIN_COURSE:
            strategy_text = f"V{i+1}\n{maneuver.strategy.value[:8]}\n"
            strategy_text += f"Δφ:{maneuver.heading_change:+.0f}°"
            
            ax.annotate(strategy_text, (vessel.position.x, vessel.position.y),
                       xytext=(15, 15), textcoords='offset points',
                       fontsize=7, ha='left',
                       bbox=dict(boxstyle='round,pad=0.3', 
                                facecolor=color, alpha=0.8))
        else:
            ax.annotate(f'{i+1}', (vessel.position.x, vessel.position.y),
                       xytext=(0, 0), textcoords='offset points',
                       ha='center', va='center', fontweight='bold', 
                       color='white', fontsize=8)
    
    _setup_maritime_plot(ax)

def _plot_trajectory_prediction(ax, vessel_states, coordination_result):
    """绘制轨迹预测"""
    
    # 预测时间范围
    time_horizon = 300  # 5分钟
    time_steps = np.arange(0, time_horizon, 30)  # 每30秒一个点
    
    for i, vessel in enumerate(vessel_states):
        if vessel.heading in [0.0, 180.0]:  # 南北向
            color = 'blue'
            alpha = 0.8
        else:  # 东西向
            color = 'red'
            alpha = 0.6
        
        # 当前位置
        ax.scatter(vessel.position.x, vessel.position.y, s=100, 
                  color=color, alpha=1.0, zorder=10)
        
        # 预测轨迹
        traj_x = []
        traj_y = []
        
        for t in time_steps:
            future_x = vessel.position.x + vessel.velocity.x * t
            future_y = vessel.position.y + vessel.velocity.y * t
            traj_x.append(future_x)
            traj_y.append(future_y)
        
        ax.plot(traj_x, traj_y, color=color, linestyle='--', 
               alpha=alpha, linewidth=2)
        
        # 终点
        if traj_x and traj_y:
            ax.scatter(traj_x[-1], traj_y[-1], s=50, color=color, 
                      marker='x', alpha=alpha)
    
    _setup_maritime_plot(ax)

def _setup_maritime_plot(ax):
    """设置海事绘图"""
    ax.set_xlim(120.99, 121.035)
    ax.set_ylim(30.985, 31.030)
    ax.set_xlabel('Longitude', fontsize=10)
    ax.set_ylabel('Latitude', fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')

def main():
    """主函数"""
    print("🌊 Complex Ferry Crossing Scenario")
    print("="*80)
    print("南北主航道双向航行 vs 东西汽渡轮穿越 - COLREGs规则9验证")
    
    # 分析场景
    analysis_result = analyze_ferry_crossing_scenario()
    
    if analysis_result:
        # 可视化
        visualize_ferry_crossing_scenario(analysis_result)
        
        print(f"\n🎯 场景特点:")
        print(f"   ✅ 10艘船舶复杂交通流")
        print(f"   ✅ 南北主航道双向航行")
        print(f"   ✅ 东西汽渡轮多点穿越")
        print(f"   ✅ COLREGs规则9自动应用")
        print(f"   ✅ 旋转卡壳精确计算")
        
        print(f"\n📁 输出: complex_ferry_crossing_scenario.png")
        print(f"✅ 复杂汽渡穿越场景演示完成!")
    
    else:
        print(f"❌ 场景分析失败!")

if __name__ == "__main__":
    main()
