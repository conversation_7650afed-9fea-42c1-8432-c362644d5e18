"""
Advanced Geometric Avoidance Demo
高级几何避让演示 - 基于理论框架的完整实现

理论基础：
1. 核心思想与几何建模 - 动态凸包和运动预测
2. 精准碰撞危险判定 - 旋转卡壳时空分析
3. 多船协同避碰策略 - 几何力场和约束优化
4. 可实现性验证 - 完整的系统集成演示
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, Circle, Arrow
import math
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'

from src_clean.geometry.advanced_rotating_calipers import (
    Point, VesselState, DynamicConvexHull, AdvancedRotatingCalipers, CollisionRisk
)
from src_clean.coordination.geometric_collision_avoidance import (
    GeometricCollisionAvoidance, AvoidanceStrategy, ManeuverPriority
)

def create_complex_maritime_scenario():
    """创建复杂海事场景 - 基于AIS数据模拟"""
    
    # 时间戳
    current_time = 1640995200.0  # 2022-01-01 00:00:00 UTC
    
    vessel_states = []
    
    # 船舶1：大型集装箱船 - 主航道东向
    vessel1 = VesselState(
        position=Point(121.000, 31.005),
        velocity=Point(0.0002, 0.0),      # 约12节东向
        heading=90.0,                     # 正东
        angular_velocity=0.0,             # 直线航行
        length=350.0,                     # 350米
        width=45.0,                       # 45米
        timestamp=current_time
    )
    vessel_states.append(vessel1)
    
    # 船舶2：散货船 - 东北向穿越主航道
    vessel2 = VesselState(
        position=Point(121.008, 30.998),
        velocity=Point(0.00012, 0.00012), # 约8节东北向
        heading=45.0,                     # 东北
        angular_velocity=0.5,             # 轻微右转
        length=280.0,                     # 280米
        width=40.0,                       # 40米
        timestamp=current_time
    )
    vessel_states.append(vessel2)
    
    # 船舶3：油轮 - 西向对遇
    vessel3 = VesselState(
        position=Point(121.018, 31.007),
        velocity=Point(-0.00015, 0.0),    # 约9节西向
        heading=270.0,                    # 正西
        angular_velocity=0.0,             # 直线航行
        length=320.0,                     # 320米
        width=50.0,                       # 50米
        timestamp=current_time
    )
    vessel_states.append(vessel3)
    
    # 船舶4：渔船 - 自由航行
    vessel4 = VesselState(
        position=Point(121.012, 31.015),
        velocity=Point(-0.00008, -0.00008), # 约4节西南向
        heading=225.0,                       # 西南
        angular_velocity=-1.0,               # 左转
        length=80.0,                         # 80米
        width=15.0,                          # 15米
        timestamp=current_time
    )
    vessel_states.append(vessel4)
    
    return vessel_states

def demonstrate_theoretical_framework():
    """演示理论框架的完整实现"""
    
    print("🧠 Advanced Geometric Collision Avoidance System")
    print("="*70)
    print("基于旋转卡壳算法的复杂水域船舶碰撞危险判定与多船协同避碰")
    
    # 创建复杂场景
    vessel_states = create_complex_maritime_scenario()
    
    print(f"\n📊 Maritime Scenario Analysis:")
    print(f"   Vessels: {len(vessel_states)}")
    for i, vessel in enumerate(vessel_states):
        speed_knots = vessel.velocity.magnitude() * 111000 / 0.514444
        print(f"   Vessel {i+1}: {vessel.length}m×{vessel.width}m, "
              f"{speed_knots:.1f}kts, heading {vessel.heading:.0f}°")
    
    # 创建几何避让系统
    avoidance_system = GeometricCollisionAvoidance(safety_domain_radius=0.003)
    
    print(f"\n🔄 Phase 1: Multi-Vessel Geometric Analysis")
    print("-" * 50)
    
    # 执行几何分析
    geometry_analysis = avoidance_system.analyze_multi_vessel_geometry(vessel_states)
    
    if 'error' in geometry_analysis:
        print(f"❌ Analysis failed: {geometry_analysis['error']}")
        return None
    
    # 显示几何分析结果
    envelope_analysis = geometry_analysis['envelope_analysis']
    print(f"   Joint Envelope: {len(envelope_analysis['joint_envelope'])} vertices")
    print(f"   Conflict Zones: {len(envelope_analysis['conflict_zones'])}")
    print(f"   Global Risk: {envelope_analysis['global_risk_assessment'].value}")
    
    # 显示成对距离分析
    print(f"\n📏 Pairwise Distance Analysis (Rotating Calipers):")
    for pair_key, data in envelope_analysis['minimum_separation_matrix'].items():
        distance_nm = data['distance'] * 60
        risk_level = data['risk_level'].value
        caliper_angle = data['caliper_angle']
        print(f"   {pair_key}: {distance_nm:.2f}nm, {risk_level}, "
              f"caliper angle {caliper_angle:.1f}°")
    
    print(f"\n⚡ Phase 2: Geometric Force Field Generation")
    print("-" * 50)
    
    # 生成几何力场
    vessel_forces = avoidance_system.generate_geometric_forces(geometry_analysis)
    
    for vessel_id, forces in vessel_forces.items():
        print(f"   Vessel {vessel_id+1}: {len(forces)} geometric forces")
        for force in forces:
            print(f"     Force: magnitude {force.magnitude:.3f}, "
                  f"direction ({force.direction.x:.3f}, {force.direction.y:.3f})")
    
    print(f"\n🎯 Phase 3: Optimal Avoidance Strategy Computation")
    print("-" * 50)
    
    # 计算避让策略
    avoidance_maneuvers = avoidance_system.compute_optimal_avoidance_strategies(
        geometry_analysis, vessel_forces
    )
    
    for maneuver in avoidance_maneuvers:
        print(f"   Vessel {maneuver.vessel_id+1}:")
        print(f"     Strategy: {maneuver.strategy.value}")
        print(f"     Priority: {maneuver.priority.value}")
        print(f"     Heading Change: {maneuver.heading_change:+.1f}°")
        print(f"     Speed Change: {maneuver.speed_change:+.1%}")
        print(f"     Execution Time: {maneuver.execution_time:.1f}s")
        print(f"     COLREGs Status: {maneuver.colregs_compliance.get('status', 'unknown')}")
    
    print(f"\n🤖 Phase 4: Dynamic Coordination Execution")
    print("-" * 50)
    
    # 执行完整协调
    coordination_result = avoidance_system.execute_dynamic_coordination(
        vessel_states, time_horizon=300.0
    )
    
    performance = coordination_result['performance_metrics']
    print(f"   Safety Improvement: {performance['safety_improvement']:.1%}")
    print(f"   Efficiency Impact: {performance['efficiency_impact']:+.1%}")
    print(f"   Coordination Quality: {performance['coordination_quality']:.1%}")
    print(f"   Computational Time: {performance['computational_time']*1000:.1f}ms")
    
    monitoring = coordination_result['monitoring_recommendations']
    print(f"   Monitoring Recommendations: {len(monitoring)} vessels")
    
    return {
        'vessel_states': vessel_states,
        'geometry_analysis': geometry_analysis,
        'vessel_forces': vessel_forces,
        'avoidance_maneuvers': avoidance_maneuvers,
        'coordination_result': coordination_result
    }

def visualize_advanced_system(demo_result):
    """可视化高级系统"""
    if not demo_result:
        return
    
    vessel_states = demo_result['vessel_states']
    geometry_analysis = demo_result['geometry_analysis']
    vessel_forces = demo_result['vessel_forces']
    avoidance_maneuvers = demo_result['avoidance_maneuvers']
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))
    
    # 子图1：几何分析
    ax1.set_title('Multi-Vessel Geometric Analysis\n(Rotating Calipers)', fontweight='bold')
    _plot_geometric_analysis(ax1, vessel_states, geometry_analysis)
    
    # 子图2：力场分析
    ax2.set_title('Geometric Force Field\n(Repulsive Forces)', fontweight='bold')
    _plot_force_field(ax2, vessel_states, vessel_forces)
    
    # 子图3：避让策略
    ax3.set_title('Avoidance Strategies\n(Optimal Maneuvers)', fontweight='bold')
    _plot_avoidance_strategies(ax3, vessel_states, avoidance_maneuvers)
    
    # 子图4：时空预测
    ax4.set_title('Spatio-Temporal Prediction\n(Collision Trajectories)', fontweight='bold')
    _plot_trajectory_prediction(ax4, vessel_states, geometry_analysis)
    
    plt.tight_layout()
    plt.savefig('advanced_geometric_avoidance_demo.png', dpi=300, bbox_inches='tight')
    plt.show()

def _plot_geometric_analysis(ax, vessel_states, geometry_analysis):
    """绘制几何分析"""
    # 绘制船舶
    hulls = geometry_analysis['hulls']
    colors = ['blue', 'red', 'green', 'purple']
    
    for i, (vessel, hull) in enumerate(zip(vessel_states, hulls)):
        color = colors[i % len(colors)]
        
        # 绘制船舶凸包
        vertices = [(v.x, v.y) for v in hull.vertices]
        x_coords, y_coords = zip(*vertices)
        ax.plot(x_coords + (x_coords[0],), y_coords + (y_coords[0],), 
               color=color, linewidth=2, alpha=0.8)
        ax.fill(x_coords, y_coords, color=color, alpha=0.3)
        
        # 标记中心
        ax.scatter(vessel.position.x, vessel.position.y, s=100, color=color, 
                  marker='o', zorder=10)
        ax.annotate(f'V{i+1}', (vessel.position.x, vessel.position.y),
                   xytext=(5, 5), textcoords='offset points', fontweight='bold')
    
    # 绘制距离线
    envelope_analysis = geometry_analysis['envelope_analysis']
    for pair_key, data in envelope_analysis['minimum_separation_matrix'].items():
        distance_nm = data['distance'] * 60
        if distance_nm < 4.0:  # 只显示4海里内的距离
            # 简化：在船舶中心间绘制线
            vessel_ids = [int(x.split('_')[1]) for x in pair_key.split('_') if x.isdigit()]
            if len(vessel_ids) == 2:
                v1, v2 = vessel_ids[0], vessel_ids[1]
                if v1 < len(vessel_states) and v2 < len(vessel_states):
                    pos1 = vessel_states[v1].position
                    pos2 = vessel_states[v2].position
                    
                    color = 'red' if distance_nm < 2.0 else 'orange'
                    ax.plot([pos1.x, pos2.x], [pos1.y, pos2.y], 
                           color=color, linewidth=2, alpha=0.7)
                    
                    # 标记距离
                    mid_x = (pos1.x + pos2.x) / 2
                    mid_y = (pos1.y + pos2.y) / 2
                    ax.annotate(f'{distance_nm:.1f}nm', (mid_x, mid_y),
                               fontsize=8, ha='center',
                               bbox=dict(boxstyle='round,pad=0.2', 
                                        facecolor=color, alpha=0.7))
    
    _setup_maritime_plot(ax)

def _plot_force_field(ax, vessel_states, vessel_forces):
    """绘制力场"""
    colors = ['blue', 'red', 'green', 'purple']
    
    for i, vessel in enumerate(vessel_states):
        color = colors[i % len(colors)]
        
        # 绘制船舶
        ax.scatter(vessel.position.x, vessel.position.y, s=200, color=color, 
                  marker='s', alpha=0.8, zorder=10)
        ax.annotate(f'V{i+1}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points', 
                   ha='center', va='center', fontweight='bold', color='white')
        
        # 绘制受力
        if i in vessel_forces:
            for force in vessel_forces[i]:
                # 力向量
                scale = 0.005  # 缩放因子
                end_x = vessel.position.x + force.direction.x * force.magnitude * scale
                end_y = vessel.position.y + force.direction.y * force.magnitude * scale
                
                ax.arrow(vessel.position.x, vessel.position.y,
                        end_x - vessel.position.x, end_y - vessel.position.y,
                        head_width=0.0005, head_length=0.0003, 
                        fc='red', ec='red', alpha=0.8)
    
    _setup_maritime_plot(ax)

def _plot_avoidance_strategies(ax, vessel_states, avoidance_maneuvers):
    """绘制避让策略"""
    colors = ['blue', 'red', 'green', 'purple']
    
    for i, (vessel, maneuver) in enumerate(zip(vessel_states, avoidance_maneuvers)):
        color = colors[i % len(colors)]
        
        # 绘制当前位置
        ax.scatter(vessel.position.x, vessel.position.y, s=200, color=color, 
                  marker='o', alpha=0.8, zorder=10)
        
        # 绘制避让动作
        if maneuver.strategy != AvoidanceStrategy.MAINTAIN_COURSE:
            # 新航向
            new_heading = vessel.heading + maneuver.heading_change
            new_heading_rad = math.radians(90 - new_heading)
            
            # 绘制新航向线
            length = 0.008
            end_x = vessel.position.x + length * math.cos(new_heading_rad)
            end_y = vessel.position.y + length * math.sin(new_heading_rad)
            
            ax.arrow(vessel.position.x, vessel.position.y,
                    end_x - vessel.position.x, end_y - vessel.position.y,
                    head_width=0.0008, head_length=0.0005, 
                    fc=color, ec=color, alpha=0.8, linewidth=3)
            
            # 标记策略
            strategy_text = f"V{i+1}: {maneuver.strategy.value}\n"
            strategy_text += f"Δφ: {maneuver.heading_change:+.0f}°\n"
            strategy_text += f"ΔV: {maneuver.speed_change:+.0%}"
            
            ax.annotate(strategy_text, (vessel.position.x, vessel.position.y),
                       xytext=(10, 10), textcoords='offset points',
                       fontsize=8, bbox=dict(boxstyle='round,pad=0.3', 
                                            facecolor=color, alpha=0.7))
    
    _setup_maritime_plot(ax)

def _plot_trajectory_prediction(ax, vessel_states, geometry_analysis):
    """绘制轨迹预测"""
    colors = ['blue', 'red', 'green', 'purple']
    
    # 绘制当前位置
    for i, vessel in enumerate(vessel_states):
        color = colors[i % len(colors)]
        ax.scatter(vessel.position.x, vessel.position.y, s=100, color=color, 
                  marker='o', zorder=10)
        
        # 预测轨迹（简化）
        time_steps = np.arange(0, 300, 30)  # 5分钟，每30秒一个点
        traj_x = []
        traj_y = []
        
        for t in time_steps:
            future_x = vessel.position.x + vessel.velocity.x * t
            future_y = vessel.position.y + vessel.velocity.y * t
            traj_x.append(future_x)
            traj_y.append(future_y)
        
        ax.plot(traj_x, traj_y, color=color, linestyle='--', alpha=0.6, linewidth=2)
        ax.scatter(traj_x[-1], traj_y[-1], s=50, color=color, marker='x')
    
    _setup_maritime_plot(ax)

def _setup_maritime_plot(ax):
    """设置海事绘图"""
    ax.set_xlim(120.99, 121.03)
    ax.set_ylim(30.99, 31.02)
    ax.set_xlabel('Longitude', fontsize=10)
    ax.set_ylabel('Latitude', fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')

def main():
    """主函数"""
    print("🌊 Advanced Geometric Collision Avoidance System")
    print("="*80)
    print("Theoretical Framework Implementation for Complex Maritime Waters")
    
    # 运行理论框架演示
    demo_result = demonstrate_theoretical_framework()
    
    if demo_result:
        # 可视化结果
        visualize_advanced_system(demo_result)
        
        print(f"\n🚀 Theoretical Framework Validation:")
        print(f"   ✅ Dynamic convex hull modeling")
        print(f"   ✅ Rotating calipers distance calculation")
        print(f"   ✅ Spatio-temporal collision prediction")
        print(f"   ✅ Geometric force field generation")
        print(f"   ✅ Multi-vessel coordination optimization")
        print(f"   ✅ COLREGs compliance verification")
        
        print(f"\n📊 System Performance:")
        performance = demo_result['coordination_result']['performance_metrics']
        print(f"   Safety Improvement: {performance['safety_improvement']:.1%}")
        print(f"   Coordination Quality: {performance['coordination_quality']:.1%}")
        print(f"   Computational Efficiency: {performance['computational_time']*1000:.1f}ms")
        
        print(f"\n📁 Output: advanced_geometric_avoidance_demo.png")
        print(f"✅ Advanced geometric avoidance demo completed!")
    
    else:
        print(f"❌ Demo failed!")

if __name__ == "__main__":
    main()
