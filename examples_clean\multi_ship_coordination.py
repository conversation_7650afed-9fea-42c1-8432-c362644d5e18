"""
Multi-Ship Coordination Demo
多船协同避碰演示 - 基于旋转卡壳算法的复杂水域协同导航
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, Rectangle, Circle
import warnings

# Suppress warnings
warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'

from src_clean.geometry.rotating_calipers import Point, ShipGeometry, RotatingCalipers
from src_clean.coordination.multi_ship_coordinator import (
    MultiShipCoordinator, ShipState, ShipType, NavigationArea, 
    ChannelGeometry, CoordinationConstraints
)

def create_complex_traffic_scenario():
    """创建复杂交通场景"""
    
    # 定义航道几何
    channel_geometry = ChannelGeometry(
        centerline=[
            Point(121.000, 31.005),
            Point(121.020, 31.005)
        ],
        width=0.010,  # 约0.6海里宽
        boundaries=[
            Point(121.000, 31.000),
            Point(121.020, 31.010)
        ],
        crossing_points=[
            Point(121.005, 31.002),
            Point(121.015, 31.008)
        ]
    )
    
    # 定义协同约束
    constraints = CoordinationConstraints(
        min_separation_distance=0.002,    # 2海里
        channel_priority_margin=0.003,    # 3海里
        crossing_safety_margin=0.004,     # 4海里
        alert_zone_radius=0.006,          # 6海里
        coordination_horizon=300.0        # 5分钟
    )
    
    # 创建船舶状态
    ship_states = []
    
    # 1. 航道内航行的大型集装箱船（最高优先级）
    ship1_geometry = ShipGeometry(
        center=Point(121.002, 31.005),
        length=350.0 / 111000,
        width=45.0 / 111000,
        heading=90.0,  # 正东向
        vertices=[]
    )
    ship1_velocity = Point(0.0002, 0.0)  # 约12节
    
    ship1_state = ShipState(
        mmsi=123456789,
        geometry=ship1_geometry,
        velocity=ship1_velocity,
        ship_type=ShipType.CHANNEL_BOUND,
        navigation_area=NavigationArea.MAIN_CHANNEL,
        priority_level=1,  # 最高优先级
        colregs_status='stand_on'
    )
    ship_states.append(ship1_state)
    
    # 2. 穿越航道的散货船
    ship2_geometry = ShipGeometry(
        center=Point(121.008, 30.998),
        length=280.0 / 111000,
        width=40.0 / 111000,
        heading=45.0,  # 东北向穿越
        vertices=[]
    )
    ship2_velocity = Point(0.00015, 0.00015)  # 约8节
    
    ship2_state = ShipState(
        mmsi=987654321,
        geometry=ship2_geometry,
        velocity=ship2_velocity,
        ship_type=ShipType.CROSSING,
        navigation_area=NavigationArea.CROSSING_AREA,
        priority_level=3,  # 较低优先级
        colregs_status='give_way'
    )
    ship_states.append(ship2_state)
    
    # 3. 另一艘穿越航道的油轮
    ship3_geometry = ShipGeometry(
        center=Point(120.998, 31.012),
        length=320.0 / 111000,
        width=50.0 / 111000,
        heading=270.0,  # 正西向穿越
        vertices=[]
    )
    ship3_velocity = Point(-0.00012, 0.0)  # 约6节
    
    ship3_state = ShipState(
        mmsi=555666777,
        geometry=ship3_geometry,
        velocity=ship3_velocity,
        ship_type=ShipType.CROSSING,
        navigation_area=NavigationArea.CROSSING_AREA,
        priority_level=3,  # 较低优先级
        colregs_status='give_way'
    )
    ship_states.append(ship3_state)
    
    # 4. 准备进入航道的船舶
    ship4_geometry = ShipGeometry(
        center=Point(120.995, 30.995),
        length=250.0 / 111000,
        width=35.0 / 111000,
        heading=60.0,  # 东北向准备进入航道
        vertices=[]
    )
    ship4_velocity = Point(0.00018, 0.00012)  # 约10节
    
    ship4_state = ShipState(
        mmsi=111222333,
        geometry=ship4_geometry,
        velocity=ship4_velocity,
        ship_type=ShipType.ENTERING_CHANNEL,
        navigation_area=NavigationArea.ALERT_ZONE,
        priority_level=2,  # 中等优先级
        colregs_status='give_way'
    )
    ship_states.append(ship4_state)
    
    return channel_geometry, constraints, ship_states

def analyze_multi_ship_coordination(channel_geometry, constraints, ship_states):
    """分析多船协同"""
    print("🚢 Multi-Ship Coordination Analysis")
    print("="*60)
    print("Advanced Rotating Calipers Algorithm for Complex Waters")
    
    # 创建协调器
    coordinator = MultiShipCoordinator(channel_geometry, constraints)
    
    # 执行协同分析
    coordination_result = coordinator.coordinate_multi_ship_avoidance(ship_states)
    
    # 显示分析结果
    print(f"\n📊 Scenario Analysis:")
    print(f"   Total Ships: {len(ship_states)}")
    
    ship_type_counts = {}
    for ship in ship_states:
        ship_type = ship.ship_type.value
        ship_type_counts[ship_type] = ship_type_counts.get(ship_type, 0) + 1
    
    for ship_type, count in ship_type_counts.items():
        print(f"   {ship_type}: {count} ships")
    
    # 几何分析结果
    geometric_analysis = coordination_result['geometric_analysis']
    print(f"\n🔄 Rotating Calipers Geometric Analysis:")
    print(f"   Pairwise Analyses: {len(geometric_analysis['pairwise_analysis'])}")
    print(f"   Crossing Conflicts: {len(geometric_analysis['crossing_conflicts'])}")
    print(f"   Alert Zone Ships: {len(geometric_analysis['alert_zone_ships'])}")
    
    # 显示成对分析详情
    print(f"\n📏 Detailed Distance Analysis:")
    for pair_key, analysis in geometric_analysis['pairwise_analysis'].items():
        ship1 = analysis['ship1']
        ship2 = analysis['ship2']
        distance_nm = analysis['distance_result']['min_distance'] * 60
        encounter_type = analysis['encounter_type']
        contact_type = analysis['distance_result']['contact_type']
        
        print(f"   {ship1.mmsi} ↔ {ship2.mmsi}: {distance_nm:.2f}nm, {encounter_type}, {contact_type}")
    
    # COLREGs规则应用
    colregs_analysis = coordination_result['colregs_analysis']
    print(f"\n⚖️  COLREGs Rules Application:")
    for pair_key, rule_app in colregs_analysis['rule_applications'].items():
        rule = rule_app['rule'].value
        result = rule_app['result']
        
        if result['give_way_vessel'] and result['stand_on_vessel']:
            print(f"   {pair_key}: {rule}")
            print(f"     Give-way: {result['give_way_vessel']}")
            print(f"     Stand-on: {result['stand_on_vessel']}")
            for req in result['specific_requirements']:
                print(f"     Requirement: {req}")
    
    # 协同群组
    coordination_groups = coordination_result['coordination_groups']
    print(f"\n👥 Coordination Groups:")
    for group_id, group_info in coordination_groups.items():
        ships = group_info['ships']
        coord_type = group_info['coordination_type']
        leader = group_info['leader']
        
        print(f"   Group {group_id}: {coord_type}")
        print(f"     Leader: {leader.mmsi}")
        print(f"     Members: {[ship.mmsi for ship in ships]}")
    
    # 协同策略
    coordination_strategies = coordination_result['coordination_strategies']
    print(f"\n🎯 Coordination Strategies:")
    for group_id, strategy in coordination_strategies.items():
        strategy_type = strategy['strategy_type']
        print(f"   Group {group_id}: {strategy_type}")
        
        for ship_mmsi, action in strategy['coordination_actions'].items():
            action_type = action['action_type']
            print(f"     Ship {ship_mmsi}: {action_type}")
    
    return coordination_result

def visualize_multi_ship_coordination(channel_geometry, ship_states, coordination_result):
    """可视化多船协同"""
    fig, ax = plt.subplots(figsize=(16, 12))
    
    # 绘制航道
    channel_width = channel_geometry.width
    centerline = channel_geometry.centerline
    
    # 绘制航道边界
    channel_rect = Rectangle((121.000, 31.000), 0.020, 0.010,
                            linewidth=2, edgecolor='blue', facecolor='lightblue',
                            alpha=0.3, label='Navigation Channel')
    ax.add_patch(channel_rect)
    
    # 绘制航道中心线
    centerline_x = [p.x for p in centerline]
    centerline_y = [p.y for p in centerline]
    ax.plot(centerline_x, centerline_y, 'b--', linewidth=2, alpha=0.7, label='Channel Centerline')
    
    # 绘制船舶
    ship_colors = {
        ShipType.CHANNEL_BOUND: 'blue',
        ShipType.CROSSING: 'red',
        ShipType.ENTERING_CHANNEL: 'green',
        ShipType.FREE_NAVIGATION: 'orange'
    }
    
    ship_labels = {
        ShipType.CHANNEL_BOUND: 'Channel-bound',
        ShipType.CROSSING: 'Crossing',
        ShipType.ENTERING_CHANNEL: 'Entering Channel',
        ShipType.FREE_NAVIGATION: 'Free Navigation'
    }
    
    for ship in ship_states:
        color = ship_colors[ship.ship_type]
        
        # 绘制船舶轮廓
        vertices = [(v.x, v.y) for v in ship.geometry.vertices]
        x_coords, y_coords = zip(*vertices)
        
        ax.plot(x_coords + (x_coords[0],), y_coords + (y_coords[0],), 
               color=color, linewidth=2, alpha=0.8)
        ax.fill(x_coords, y_coords, color=color, alpha=0.3)
        
        # 标记船舶中心和MMSI
        center = ship.geometry.center
        ax.scatter(center.x, center.y, s=100, color=color, marker='o', zorder=10)
        ax.annotate(f'{ship.mmsi}\nP{ship.priority_level}', 
                   (center.x, center.y), xytext=(5, 5), textcoords='offset points',
                   fontsize=8, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
        
        # 绘制速度向量
        arrow_length = 0.003
        ax.arrow(center.x, center.y,
                ship.velocity.x * arrow_length * 100,
                ship.velocity.y * arrow_length * 100,
                head_width=0.0003, head_length=0.0002, fc=color, ec=color, alpha=0.7)
    
    # 绘制距离线和冲突标记
    geometric_analysis = coordination_result['geometric_analysis']
    for pair_key, analysis in geometric_analysis['pairwise_analysis'].items():
        distance_result = analysis['distance_result']
        closest_points = distance_result['closest_points']
        
        if closest_points['ship1'] and closest_points['ship2']:
            cp1 = closest_points['ship1']
            cp2 = closest_points['ship2']
            
            distance_nm = distance_result['min_distance'] * 60
            
            # 根据距离设置颜色
            if distance_nm < 1.0:
                line_color = 'red'
                line_style = '-'
                alpha = 0.8
            elif distance_nm < 2.0:
                line_color = 'orange'
                line_style = '--'
                alpha = 0.6
            else:
                line_color = 'green'
                line_style = ':'
                alpha = 0.4
            
            ax.plot([cp1.x, cp2.x], [cp1.y, cp2.y], 
                   color=line_color, linestyle=line_style, linewidth=2, alpha=alpha)
            
            # 标记距离
            mid_x = (cp1.x + cp2.x) / 2
            mid_y = (cp1.y + cp2.y) / 2
            ax.annotate(f'{distance_nm:.2f}nm', (mid_x, mid_y),
                       xytext=(0, 0), textcoords='offset points',
                       fontsize=8, ha='center',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor=line_color, alpha=0.7))
    
    # 设置图形属性
    ax.set_xlim(120.99, 121.03)
    ax.set_ylim(30.99, 31.02)
    ax.set_xlabel('Longitude', fontsize=12)
    ax.set_ylabel('Latitude', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')
    
    # 创建图例
    legend_elements = []
    for ship_type, color in ship_colors.items():
        if any(ship.ship_type == ship_type for ship in ship_states):
            legend_elements.append(plt.Line2D([0], [0], marker='s', color='w', 
                                            markerfacecolor=color, markersize=10,
                                            label=ship_labels[ship_type]))
    
    ax.legend(handles=legend_elements, loc='upper right')
    
    ax.set_title('Multi-Ship Coordination System\n'
                'Rotating Calipers Algorithm for Complex Waters', 
                fontsize=14, fontweight='bold')
    
    # 添加信息框
    info_text = f"""COORDINATION ANALYSIS SUMMARY

🚢 Ships: {len(ship_states)}
🔄 Algorithm: Rotating Calipers
⚖️  Rules: COLREGs Compliant
👥 Groups: {len(coordination_result['coordination_groups'])}

SHIP PRIORITIES:
P1: Channel-bound (Highest)
P2: Entering Channel
P3: Crossing Vessels

DISTANCE CODING:
Red: < 1nm (Critical)
Orange: 1-2nm (Caution)
Green: > 2nm (Safe)"""
    
    ax.text(0.02, 0.02, info_text, transform=ax.transAxes,
           verticalalignment='bottom', fontsize=9, fontfamily='monospace',
           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))
    
    plt.tight_layout()
    plt.savefig('multi_ship_coordination.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""
    print("🌊 Multi-Ship Coordination System Demo")
    print("="*70)
    print("Advanced Rotating Calipers Algorithm for Complex Maritime Traffic")
    
    # 创建复杂交通场景
    channel_geometry, constraints, ship_states = create_complex_traffic_scenario()
    
    print(f"\n📋 Scenario Setup:")
    print(f"   Channel Width: {channel_geometry.width * 60:.1f} nautical miles")
    print(f"   Coordination Horizon: {constraints.coordination_horizon / 60:.1f} minutes")
    print(f"   Safety Margins: {constraints.crossing_safety_margin * 60:.1f}nm crossing")
    
    # 执行多船协同分析
    coordination_result = analyze_multi_ship_coordination(channel_geometry, constraints, ship_states)
    
    # 可视化结果
    visualize_multi_ship_coordination(channel_geometry, ship_states, coordination_result)
    
    print(f"\n🚀 Technical Achievements:")
    print(f"   ✅ Multi-ship rotating calipers analysis")
    print(f"   ✅ COLREGs rules automatic application")
    print(f"   ✅ Channel crossing coordination")
    print(f"   ✅ Priority-based conflict resolution")
    print(f"   ✅ Complex water traffic management")
    
    print(f"\n📁 Output: multi_ship_coordination.png")
    print(f"✅ Multi-ship coordination demo completed!")

if __name__ == "__main__":
    main()
