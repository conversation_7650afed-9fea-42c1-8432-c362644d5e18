{"python.defaultInterpreter": "./RotateClipperEnv/Scripts/python.exe", "python.pythonPath": "./RotateClipperEnv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.terminal.activateEnvInCurrentTerminal": true, "files.associations": {"*.py": "python"}, "terminal.integrated.defaultProfile.windows": "Command Prompt", "mcp.mcpServers": {"time": {"command": "./RotateClipperEnv/Scripts/python.exe", "args": ["-m", "mcp_server_time"], "env": {"PYTHONPATH": "${workspaceFolder}"}}}}