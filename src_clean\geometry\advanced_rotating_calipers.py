"""
Advanced Rotating Calipers for Maritime Collision Avoidance
高级旋转卡壳算法 - 复杂水域船舶碰撞危险判定与多船协同避碰

基于理论原理：
1. 动态凸包建模 - 将船舶视为运动凸多边形
2. 时空预测分析 - 预测未来时间内的碰撞趋势
3. 几何高效性 - O(n+m)复杂度的精确距离计算
4. 全局空间感知 - 多船联合包络分析
"""

import numpy as np
import math
from typing import List, Dict, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum

@dataclass
class Point:
    """二维点类 - 支持向量运算"""
    x: float
    y: float

    def __add__(self, other):
        return Point(self.x + other.x, self.y + other.y)

    def __sub__(self, other):
        return Point(self.x - other.x, self.y - other.y)

    def __mul__(self, scalar: float):
        return Point(self.x * scalar, self.y * scalar)

    def __truediv__(self, scalar: float):
        return Point(self.x / scalar, self.y / scalar)

    def distance_to(self, other) -> float:
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)

    def magnitude(self) -> float:
        return math.sqrt(self.x**2 + self.y**2)

    def normalize(self):
        mag = self.magnitude()
        return Point(self.x / mag, self.y / mag) if mag > 0 else Point(0, 0)

    def dot(self, other) -> float:
        return self.x * other.x + self.y * other.y

    def cross(self, other) -> float:
        return self.x * other.y - self.y * other.x

@dataclass
class VesselState:
    """船舶状态 - AIS数据基础"""
    position: Point          # 位置 p_i(t)
    velocity: Point          # 速度向量 v_i(t)
    heading: float           # 航向角 ψ_i(t) (度)
    angular_velocity: float  # 角速度 (度/秒)
    length: float           # 船长 (米)
    width: float            # 船宽 (米)
    timestamp: float        # 时间戳

class CollisionRisk(Enum):
    """碰撞风险等级"""
    SAFE = "SAFE"                    # 安全
    CAUTION = "CAUTION"             # 注意
    WARNING = "WARNING"             # 警告
    CRITICAL = "CRITICAL"           # 危险
    IMMINENT = "IMMINENT"           # 紧急

@dataclass
class DynamicConvexHull:
    """动态凸包 - 船舶几何建模"""
    vertices: List[Point]           # 凸包顶点
    center: Point                   # 几何中心
    orientation: float              # 方向角
    velocity: Point                 # 运动速度
    angular_velocity: float         # 角速度

    def predict_position(self, time_delta: float):
        """预测未来位置"""
        future_center = Point(
            self.center.x + self.velocity.x * time_delta,
            self.center.y + self.velocity.y * time_delta
        )
        future_orientation = self.orientation + self.angular_velocity * time_delta

        return self._generate_hull_at_position(future_center, future_orientation)

    def _generate_hull_at_position(self, center: Point, orientation: float):
        """在指定位置生成凸包"""
        # 基于中心点和方向角重新计算顶点
        cos_theta = math.cos(math.radians(orientation))
        sin_theta = math.sin(math.radians(orientation))

        new_vertices = []
        for vertex in self.vertices:
            # 相对于原中心的偏移
            dx = vertex.x - self.center.x
            dy = vertex.y - self.center.y

            # 旋转变换
            new_dx = dx * cos_theta - dy * sin_theta
            new_dy = dx * sin_theta + dy * cos_theta

            new_vertices.append(Point(center.x + new_dx, center.y + new_dy))

        return DynamicConvexHull(
            vertices=new_vertices,
            center=center,
            orientation=orientation,
            velocity=self.velocity,
            angular_velocity=self.angular_velocity
        )

class AdvancedRotatingCalipers:
    """高级旋转卡壳算法 - 海事应用特化版本"""

    def __init__(self, safety_threshold: float = 0.002):  # 2海里安全阈值
        self.safety_threshold = safety_threshold
        self.tolerance = 1e-10

    def create_vessel_hull(self, vessel_state: VesselState) -> DynamicConvexHull:
        """根据AIS数据创建船舶动态凸包"""
        # 船舶几何参数
        half_length = vessel_state.length / 2 / 111000  # 转换为度
        half_width = vessel_state.width / 2 / 111000

        # 船舶轮廓点（相对坐标）- 更精确的7点模型
        relative_points = [
            Point(half_length, 0),                          # 船首
            Point(half_length * 0.7, half_width * 0.8),     # 右前
            Point(-half_length * 0.2, half_width),          # 右中
            Point(-half_length, half_width * 0.6),          # 右后
            Point(-half_length, -half_width * 0.6),         # 左后
            Point(-half_length * 0.2, -half_width),         # 左中
            Point(half_length * 0.7, -half_width * 0.8),    # 左前
        ]

        # 转换为全局坐标
        heading_rad = math.radians(vessel_state.heading)
        cos_h = math.cos(heading_rad)
        sin_h = math.sin(heading_rad)

        global_vertices = []
        for point in relative_points:
            # 旋转变换
            rotated_x = point.x * cos_h - point.y * sin_h
            rotated_y = point.x * sin_h + point.y * cos_h

            global_vertices.append(Point(
                vessel_state.position.x + rotated_x,
                vessel_state.position.y + rotated_y
            ))

        return DynamicConvexHull(
            vertices=global_vertices,
            center=vessel_state.position,
            orientation=vessel_state.heading,
            velocity=vessel_state.velocity,
            angular_velocity=vessel_state.angular_velocity
        )

    def rotating_calipers_distance(self, hull1: DynamicConvexHull,
                                 hull2: DynamicConvexHull) -> Dict:
        """
        旋转卡壳距离计算 - 核心算法

        Returns:
            {
                'min_distance': float,           # 最小距离
                'closest_points': dict,          # 最近点对
                'contact_type': str,             # 接触类型
                'separation_vector': Point,      # 分离向量
                'caliper_angle': float,          # 卡壳角度
                'geometric_features': dict       # 几何特征
            }
        """
        vertices1 = hull1.vertices
        vertices2 = hull2.vertices

        if not vertices1 or not vertices2:
            return self._empty_result()

        # 确保凸包逆时针排列
        vertices1 = self._ensure_ccw(vertices1)
        vertices2 = self._ensure_ccw(vertices2)

        min_distance = float('inf')
        closest_point1 = None
        closest_point2 = None
        contact_type = "separated"
        optimal_caliper_angle = 0.0

        n1, n2 = len(vertices1), len(vertices2)

        # 旋转卡壳主算法 - 改进版本
        i = self._find_extreme_point(vertices1, Point(1, 0))  # 最右点
        j = self._find_extreme_point(vertices2, Point(-1, 0)) # 最左点

        start_i, start_j = i, j
        angle = 0.0

        # 执行旋转卡壳
        while angle < 2 * math.pi:
            # 当前点对距离
            current_dist = vertices1[i].distance_to(vertices2[j])
            if current_dist < min_distance:
                min_distance = current_dist
                closest_point1 = vertices1[i]
                closest_point2 = vertices2[j]
                contact_type = "vertex-vertex"
                optimal_caliper_angle = math.degrees(angle)

            # 检查点到边距离
            self._check_point_to_edge_distances(
                vertices1, vertices2, i, j, min_distance,
                closest_point1, closest_point2, contact_type
            )

            # 计算下一步旋转
            next_i = (i + 1) % n1
            next_j = (j + 1) % n2

            # 边向量
            edge1 = vertices1[next_i] - vertices1[i]
            edge2 = vertices2[next_j] - vertices2[j]

            # 计算边与当前方向的夹角
            angle1 = math.atan2(edge1.y, edge1.x) - angle
            angle2 = math.atan2(edge2.y, edge2.x) - angle + math.pi

            # 标准化角度
            angle1 = self._normalize_angle(angle1)
            angle2 = self._normalize_angle(angle2)

            # 选择较小的角度步进
            if angle1 < angle2:
                angle += angle1
                i = next_i
            elif angle2 < angle1:
                angle += angle2
                j = next_j
            else:
                angle += angle1
                i = next_i
                j = next_j

            # 防止无限循环
            if angle >= 2 * math.pi or (i == start_i and j == start_j and angle > 0):
                break

        # 计算分离向量和几何特征
        separation_vector = None
        geometric_features = {}

        if closest_point1 and closest_point2:
            separation_vector = closest_point2 - closest_point1
            geometric_features = self._analyze_geometric_features(
                hull1, hull2, closest_point1, closest_point2, min_distance
            )

        return {
            'min_distance': min_distance,
            'closest_points': {
                'hull1': closest_point1,
                'hull2': closest_point2
            },
            'contact_type': contact_type,
            'separation_vector': separation_vector,
            'caliper_angle': optimal_caliper_angle,
            'geometric_features': geometric_features
        }

    def predict_collision_trajectory(self, hull1: DynamicConvexHull,
                                   hull2: DynamicConvexHull,
                                   time_horizon: float = 300.0,
                                   time_step: float = 5.0) -> Dict:
        """
        预测碰撞轨迹 - 时空协同分析

        Args:
            hull1, hull2: 动态凸包
            time_horizon: 预测时间范围（秒）
            time_step: 时间步长（秒）

        Returns:
            碰撞预测结果
        """
        trajectory_analysis = {
            'collision_predicted': False,
            'collision_time': None,
            'closest_approach_time': None,
            'min_predicted_distance': float('inf'),
            'distance_trend': [],
            'risk_evolution': [],
            'critical_points': []
        }

        steps = int(time_horizon / time_step)

        for step in range(steps):
            t = step * time_step

            # 预测未来位置
            future_hull1 = hull1.predict_position(t)
            future_hull2 = hull2.predict_position(t)

            # 计算未来距离
            distance_result = self.rotating_calipers_distance(future_hull1, future_hull2)
            distance = distance_result['min_distance']

            # 记录轨迹
            trajectory_analysis['distance_trend'].append({
                'time': t,
                'distance': distance,
                'caliper_angle': distance_result['caliper_angle']
            })

            # 更新最小距离
            if distance < trajectory_analysis['min_predicted_distance']:
                trajectory_analysis['min_predicted_distance'] = distance
                trajectory_analysis['closest_approach_time'] = t

            # 风险评估
            risk_level = self._assess_collision_risk(distance)
            trajectory_analysis['risk_evolution'].append({
                'time': t,
                'risk_level': risk_level
            })

            # 检查碰撞
            if distance < 0.0001:  # 碰撞阈值
                trajectory_analysis['collision_predicted'] = True
                trajectory_analysis['collision_time'] = t
                break

            # 记录关键点
            if risk_level in [CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
                trajectory_analysis['critical_points'].append({
                    'time': t,
                    'distance': distance,
                    'risk_level': risk_level,
                    'geometric_features': distance_result['geometric_features']
                })

        return trajectory_analysis

    def multi_vessel_envelope_analysis(self, hulls: List[DynamicConvexHull]) -> Dict:
        """
        多船联合包络分析 - 全局空间感知

        Args:
            hulls: 多个动态凸包列表

        Returns:
            联合包络分析结果
        """
        if len(hulls) < 2:
            return {'error': 'At least 2 vessels required'}

        envelope_analysis = {
            'joint_envelope': None,
            'conflict_zones': [],
            'minimum_separation_matrix': {},
            'global_risk_assessment': CollisionRisk.SAFE,
            'coordination_recommendations': []
        }

        # 构造联合包络
        all_vertices = []
        for hull in hulls:
            all_vertices.extend(hull.vertices)

        joint_envelope = self._compute_convex_hull(all_vertices)
        envelope_analysis['joint_envelope'] = joint_envelope

        # 成对分析
        for i in range(len(hulls)):
            for j in range(i + 1, len(hulls)):
                hull1, hull2 = hulls[i], hulls[j]

                # 距离分析
                distance_result = self.rotating_calipers_distance(hull1, hull2)
                pair_key = f"vessel_{i}_vessel_{j}"

                envelope_analysis['minimum_separation_matrix'][pair_key] = {
                    'distance': distance_result['min_distance'],
                    'risk_level': self._assess_collision_risk(distance_result['min_distance']),
                    'caliper_angle': distance_result['caliper_angle'],
                    'geometric_features': distance_result['geometric_features']
                }

                # 冲突区域检测
                if distance_result['min_distance'] < self.safety_threshold:
                    conflict_zone = self._identify_conflict_zone(hull1, hull2, distance_result)
                    envelope_analysis['conflict_zones'].append(conflict_zone)

        # 全局风险评估
        envelope_analysis['global_risk_assessment'] = self._assess_global_risk(
            envelope_analysis['minimum_separation_matrix']
        )

        # 协调建议
        envelope_analysis['coordination_recommendations'] = self._generate_coordination_recommendations(
            hulls, envelope_analysis
        )

        return envelope_analysis

    # 辅助方法
    def _ensure_ccw(self, vertices: List[Point]) -> List[Point]:
        """确保顶点逆时针排列"""
        if len(vertices) < 3:
            return vertices

        # 计算有向面积
        area = 0
        n = len(vertices)
        for i in range(n):
            j = (i + 1) % n
            area += (vertices[j].x - vertices[i].x) * (vertices[j].y + vertices[i].y)

        return vertices[::-1] if area > 0 else vertices

    def _find_extreme_point(self, vertices: List[Point], direction: Point) -> int:
        """找到指定方向的极值点"""
        max_dot = float('-inf')
        extreme_idx = 0

        for i, vertex in enumerate(vertices):
            dot_product = vertex.dot(direction)
            if dot_product > max_dot:
                max_dot = dot_product
                extreme_idx = i

        return extreme_idx

    def _normalize_angle(self, angle: float) -> float:
        """标准化角度到[0, 2π]"""
        while angle < 0:
            angle += 2 * math.pi
        while angle >= 2 * math.pi:
            angle -= 2 * math.pi
        return angle

    def _assess_collision_risk(self, distance: float) -> CollisionRisk:
        """评估碰撞风险等级"""
        distance_nm = distance * 60  # 转换为海里

        if distance_nm < 0.5:
            return CollisionRisk.IMMINENT
        elif distance_nm < 1.0:
            return CollisionRisk.CRITICAL
        elif distance_nm < 2.0:
            return CollisionRisk.WARNING
        elif distance_nm < 4.0:
            return CollisionRisk.CAUTION
        else:
            return CollisionRisk.SAFE

    def _analyze_geometric_features(self, hull1: DynamicConvexHull, hull2: DynamicConvexHull,
                                  cp1: Point, cp2: Point, distance: float) -> Dict:
        """分析几何特征"""
        return {
            'relative_velocity': hull2.velocity - hull1.velocity,
            'relative_heading': hull2.orientation - hull1.orientation,
            'approach_angle': math.degrees(math.atan2(cp2.y - cp1.y, cp2.x - cp1.x)),
            'size_ratio': (hull1.center.distance_to(hull1.vertices[0]) /
                          hull2.center.distance_to(hull2.vertices[0])),
            'distance_nautical_miles': distance * 60
        }

    def _check_point_to_edge_distances(self, vertices1, vertices2, i, j,
                                      min_distance, closest_point1, closest_point2, contact_type):
        """检查点到边的距离"""
        # 简化实现 - 在实际应用中需要完整的点到边距离计算
        pass

    def _identify_conflict_zone(self, hull1: DynamicConvexHull, hull2: DynamicConvexHull,
                              distance_result: Dict) -> Dict:
        """识别冲突区域"""
        return {
            'zone_center': distance_result['closest_points']['hull1'],
            'zone_radius': distance_result['min_distance'],
            'conflict_type': distance_result['contact_type'],
            'severity': self._assess_collision_risk(distance_result['min_distance'])
        }

    def _assess_global_risk(self, separation_matrix: Dict) -> CollisionRisk:
        """评估全局风险"""
        max_risk = CollisionRisk.SAFE
        for pair_data in separation_matrix.values():
            risk = pair_data['risk_level']
            if risk.value > max_risk.value:
                max_risk = risk
        return max_risk

    def _generate_coordination_recommendations(self, hulls: List[DynamicConvexHull],
                                             envelope_analysis: Dict) -> List[Dict]:
        """生成协调建议"""
        recommendations = []

        for i, hull in enumerate(hulls):
            recommendation = {
                'vessel_id': i,
                'action_type': 'MAINTAIN_COURSE',
                'priority': 'NORMAL',
                'suggested_heading_change': 0.0,
                'suggested_speed_change': 0.0,
                'reasoning': 'No immediate conflict detected'
            }

            # 基于风险等级调整建议
            global_risk = envelope_analysis['global_risk_assessment']
            if global_risk in [CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
                recommendation.update({
                    'action_type': 'EMERGENCY_MANEUVER',
                    'priority': 'HIGH',
                    'suggested_heading_change': 30.0,  # 右转30度
                    'suggested_speed_change': -0.3,    # 减速30%
                    'reasoning': 'Critical collision risk detected'
                })
            elif global_risk == CollisionRisk.WARNING:
                recommendation.update({
                    'action_type': 'PRECAUTIONARY_MANEUVER',
                    'priority': 'MEDIUM',
                    'suggested_heading_change': 15.0,  # 右转15度
                    'suggested_speed_change': -0.1,    # 减速10%
                    'reasoning': 'Warning level risk detected'
                })

            recommendations.append(recommendation)

        return recommendations

    def _compute_convex_hull(self, points: List[Point]) -> List[Point]:
        """计算凸包 - Graham扫描法"""
        if len(points) < 3:
            return points

        # 找到最下方的点（y最小，x最小）
        start = min(points, key=lambda p: (p.y, p.x))

        # 按极角排序
        def polar_angle(p):
            dx = p.x - start.x
            dy = p.y - start.y
            return math.atan2(dy, dx)

        sorted_points = sorted([p for p in points if p != start], key=polar_angle)

        # Graham扫描
        hull = [start]
        for point in sorted_points:
            while len(hull) > 1 and self._cross_product(
                hull[-1] - hull[-2], point - hull[-1]
            ) <= 0:
                hull.pop()
            hull.append(point)

        return hull

    def _cross_product(self, v1: Point, v2: Point) -> float:
        """计算叉积"""
        return v1.x * v2.y - v1.y * v2.x

    def _empty_result(self) -> Dict:
        """空结果"""
        return {
            'min_distance': float('inf'),
            'closest_points': {'hull1': None, 'hull2': None},
            'contact_type': 'no_data',
            'separation_vector': None,
            'caliper_angle': 0.0,
            'geometric_features': {}
        }
