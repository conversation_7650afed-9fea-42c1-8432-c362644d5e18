"""
Dynamic Avoidance Animator
动态避让动画器 - 基于旋转卡壳的实时多船协同避碰
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import Polygon, Circle, Rectangle
import math
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

from ..geometry.rotating_calipers import Point, ShipGeometry, RotatingCalipers
from ..coordination.multi_ship_coordinator import (
    MultiShipCoordinator, ShipState, ShipType, NavigationArea, 
    ChannelGeometry, CoordinationConstraints
)

@dataclass
class DynamicShipState:
    """动态船舶状态"""
    mmsi: int
    x: float
    y: float
    heading: float
    speed_knots: float
    length: float
    width: float
    ship_type: ShipType
    navigation_area: NavigationArea
    priority_level: int
    color: str
    
    # 动态状态
    trajectory_x: List[float]
    trajectory_y: List[float]
    predicted_x: List[float]
    predicted_y: List[float]
    
    # 避让状态
    avoidance_active: bool = False
    original_heading: float = None
    original_speed: float = None
    avoidance_start_time: float = None
    
    def __post_init__(self):
        if self.trajectory_x is None:
            self.trajectory_x = [self.x]
        if self.trajectory_y is None:
            self.trajectory_y = [self.y]
        if self.predicted_x is None:
            self.predicted_x = []
        if self.predicted_y is None:
            self.predicted_y = []
        if self.original_heading is None:
            self.original_heading = self.heading
        if self.original_speed is None:
            self.original_speed = self.speed_knots

class DynamicAvoidanceAnimator:
    """动态避让动画器"""
    
    def __init__(self, figsize=(16, 12)):
        """初始化动画器"""
        self.fig, self.ax = plt.subplots(figsize=figsize)
        self.ships = {}
        self.ship_patches = {}
        self.trajectory_lines = {}
        self.prediction_lines = {}
        self.conflict_markers = []
        self.safety_zones = []
        
        # 算法组件
        self.calipers = RotatingCalipers()
        self.coordinator = None
        
        # 动画参数
        self.dt = 4.0  # 时间步长（秒）
        self.frame_count = 0
        self.current_time = 0.0
        self.max_frames = 200
        
        # 动态避让状态
        self.coordination_active = False
        self.coordination_start_time = None
        self.global_coordination_result = None
        
        self._setup_plot()
    
    def _setup_plot(self):
        """设置绘图环境"""
        self.ax.set_xlim(120.99, 121.03)
        self.ax.set_ylim(30.99, 31.02)
        self.ax.set_xlabel('Longitude', fontsize=12)
        self.ax.set_ylabel('Latitude', fontsize=12)
        self.ax.grid(True, alpha=0.3)
        self.ax.set_aspect('equal')
        
        # 绘制航道
        self._draw_navigation_channel()
        
        # 添加标题
        self.title = self.ax.set_title('Dynamic Multi-Ship Collision Avoidance\n'
                                      'Real-time Rotating Calipers Coordination', 
                                      fontsize=14, fontweight='bold')
        
        # 信息文本框
        self.info_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes,
                                     verticalalignment='top', fontsize=10,
                                     bbox=dict(boxstyle='round,pad=0.5', 
                                              facecolor='lightcyan', alpha=0.9))
    
    def _draw_navigation_channel(self):
        """绘制航道"""
        # 主航道
        channel_rect = Rectangle((121.000, 31.000), 0.020, 0.010,
                                linewidth=2, edgecolor='blue', facecolor='lightblue',
                                alpha=0.2, label='Main Channel')
        self.ax.add_patch(channel_rect)
        
        # 航道中心线
        self.ax.plot([121.000, 121.020], [31.005, 31.005], 'b--', 
                    linewidth=2, alpha=0.7, label='Channel Centerline')
        
        # 穿越区域
        crossing_area1 = Circle((121.005, 31.002), 0.003, 
                               color='orange', alpha=0.2, label='Crossing Area')
        crossing_area2 = Circle((121.015, 31.008), 0.003, 
                               color='orange', alpha=0.2)
        self.ax.add_patch(crossing_area1)
        self.ax.add_patch(crossing_area2)
        
        # 警戒区
        alert_zone = Circle((121.010, 31.005), 0.008, 
                           color='yellow', alpha=0.1, linestyle='--',
                           fill=False, linewidth=2, label='Alert Zone')
        self.ax.add_patch(alert_zone)
    
    def add_ship(self, ship: DynamicShipState):
        """添加船舶到动画"""
        self.ships[ship.mmsi] = ship
        
        # 创建船舶多边形
        vertices = self._get_ship_vertices(ship)
        patch = Polygon(vertices, closed=True, facecolor=ship.color, 
                       edgecolor='black', alpha=0.8, linewidth=2)
        self.ax.add_patch(patch)
        self.ship_patches[ship.mmsi] = patch
        
        # 创建轨迹线
        self.trajectory_lines[ship.mmsi], = self.ax.plot([], [], 
                                                        color=ship.color, 
                                                        linewidth=2, alpha=0.6,
                                                        label=f'Ship {ship.mmsi}')
        
        # 创建预测轨迹线
        self.prediction_lines[ship.mmsi], = self.ax.plot([], [], 
                                                        color=ship.color, 
                                                        linewidth=2, linestyle=':', alpha=0.8)
    
    def _get_ship_vertices(self, ship: DynamicShipState) -> List[Tuple[float, float]]:
        """获取船舶顶点"""
        half_length = ship.length / 2 / 111000
        half_width = ship.width / 2 / 111000
        
        # 船舶轮廓点
        relative_points = [
            (half_length, 0),                    # 船首
            (half_length * 0.6, half_width * 0.8),
            (-half_length * 0.3, half_width),
            (-half_length, half_width * 0.6),
            (-half_length, -half_width * 0.6),
            (-half_length * 0.3, -half_width),
            (half_length * 0.6, -half_width * 0.8),
        ]
        
        # 旋转到航向
        math_angle = math.radians(90 - ship.heading)
        cos_h = math.cos(math_angle)
        sin_h = math.sin(math_angle)
        
        rotated_vertices = []
        for vx, vy in relative_points:
            rx = vx * cos_h - vy * sin_h
            ry = vx * sin_h + vy * cos_h
            rotated_vertices.append((ship.x + rx, ship.y + ry))
        
        return rotated_vertices
    
    def _calculate_velocity_components(self, ship: DynamicShipState) -> Tuple[float, float]:
        """计算速度分量"""
        speed_deg_per_sec = ship.speed_knots * 0.514444 / 111000
        math_angle = math.radians(90 - ship.heading)
        vx = speed_deg_per_sec * math.cos(math_angle)
        vy = speed_deg_per_sec * math.sin(math_angle)
        return vx, vy
    
    def _update_ship_position(self, ship: DynamicShipState, dt: float):
        """更新船舶位置"""
        vx, vy = self._calculate_velocity_components(ship)
        
        # 更新位置
        ship.x += vx * dt
        ship.y += vy * dt
        
        # 记录轨迹
        ship.trajectory_x.append(ship.x)
        ship.trajectory_y.append(ship.y)
        
        # 限制轨迹长度
        if len(ship.trajectory_x) > 50:
            ship.trajectory_x = ship.trajectory_x[-50:]
            ship.trajectory_y = ship.trajectory_y[-50:]
    
    def _predict_ship_trajectory(self, ship: DynamicShipState, prediction_time: float = 120.0):
        """预测船舶轨迹"""
        ship.predicted_x = []
        ship.predicted_y = []
        
        vx, vy = self._calculate_velocity_components(ship)
        
        # 预测未来轨迹
        for t in range(0, int(prediction_time), 10):  # 每10秒一个点
            future_x = ship.x + vx * t
            future_y = ship.y + vy * t
            ship.predicted_x.append(future_x)
            ship.predicted_y.append(future_y)
    
    def _detect_coordination_trigger(self) -> bool:
        """检测是否需要启动协调"""
        if self.coordination_active:
            return False
        
        # 检查船舶间距离
        ships_list = list(self.ships.values())
        for i in range(len(ships_list)):
            for j in range(i + 1, len(ships_list)):
                ship1 = ships_list[i]
                ship2 = ships_list[j]
                
                # 计算距离
                dx = ship2.x - ship1.x
                dy = ship2.y - ship1.y
                distance = math.sqrt(dx**2 + dy**2)
                distance_nm = distance * 60  # 转换为海里
                
                # 如果距离小于6海里，启动协调
                if distance_nm < 6.0:
                    return True
        
        return False
    
    def _execute_coordination(self):
        """执行协调"""
        if not self.coordinator:
            # 创建协调器
            channel_geometry = ChannelGeometry(
                centerline=[Point(121.000, 31.005), Point(121.020, 31.005)],
                width=0.010,
                boundaries=[Point(121.000, 31.000), Point(121.020, 31.010)],
                crossing_points=[Point(121.005, 31.002), Point(121.015, 31.008)]
            )
            
            constraints = CoordinationConstraints(
                min_separation_distance=0.002,
                channel_priority_margin=0.003,
                crossing_safety_margin=0.004,
                alert_zone_radius=0.006,
                coordination_horizon=300.0
            )
            
            self.coordinator = MultiShipCoordinator(channel_geometry, constraints)
        
        # 创建船舶状态
        ship_states = self._create_ship_states_for_coordination()
        
        # 执行协调分析
        try:
            self.global_coordination_result = self.coordinator.coordinate_multi_ship_avoidance(ship_states)
            self.coordination_active = True
            self.coordination_start_time = self.current_time
            
            # 应用协调策略
            self._apply_coordination_strategies()
            
        except Exception as e:
            print(f"Coordination failed: {e}")
    
    def _create_ship_states_for_coordination(self) -> List[ShipState]:
        """为协调算法创建船舶状态"""
        ship_states = []
        
        for ship in self.ships.values():
            # 创建几何
            geometry = ShipGeometry(
                center=Point(ship.x, ship.y),
                length=ship.length / 111000,
                width=ship.width / 111000,
                heading=ship.heading,
                vertices=[]
            )
            
            # 创建速度
            vx, vy = self._calculate_velocity_components(ship)
            velocity = Point(vx, vy)
            
            # 创建状态
            state = ShipState(
                mmsi=ship.mmsi,
                geometry=geometry,
                velocity=velocity,
                ship_type=ship.ship_type,
                navigation_area=ship.navigation_area,
                priority_level=ship.priority_level,
                colregs_status='unknown'
            )
            
            ship_states.append(state)
        
        return ship_states
    
    def _apply_coordination_strategies(self):
        """应用协调策略"""
        if not self.global_coordination_result:
            return
        
        strategies = self.global_coordination_result.get('coordination_strategies', {})
        
        for group_id, strategy in strategies.items():
            coordination_actions = strategy.get('coordination_actions', {})
            
            for ship_mmsi, action in coordination_actions.items():
                if ship_mmsi in self.ships:
                    ship = self.ships[ship_mmsi]
                    self._apply_ship_action(ship, action)
    
    def _apply_ship_action(self, ship: DynamicShipState, action: Dict):
        """应用船舶动作"""
        action_type = action.get('action_type', '')
        
        if not ship.avoidance_active:
            ship.avoidance_active = True
            ship.avoidance_start_time = self.current_time
            
            # 根据船舶类型应用不同的避让策略
            if ship.ship_type == ShipType.CROSSING:
                # 穿越船舶：大幅度避让
                ship.heading = (ship.heading - 25.0) % 360
                ship.speed_knots = max(2.0, ship.speed_knots - 3.0)
            elif ship.ship_type == ShipType.FREE_NAVIGATION:
                # 自由航行船舶：适度避让
                ship.heading = (ship.heading + 15.0) % 360
                ship.speed_knots = max(2.0, ship.speed_knots - 2.0)
    
    def _update_visualization_elements(self):
        """更新可视化元素"""
        # 清除之前的标记
        for marker in self.conflict_markers:
            marker.remove()
        self.conflict_markers = []
        
        for zone in self.safety_zones:
            zone.remove()
        self.safety_zones = []
        
        # 更新船舶
        for ship in self.ships.values():
            # 更新船舶多边形
            vertices = self._get_ship_vertices(ship)
            self.ship_patches[ship.mmsi].set_xy(vertices)
            
            # 更新轨迹线
            self.trajectory_lines[ship.mmsi].set_data(ship.trajectory_x, ship.trajectory_y)
            
            # 更新预测轨迹线
            if ship.predicted_x and ship.predicted_y:
                self.prediction_lines[ship.mmsi].set_data(ship.predicted_x, ship.predicted_y)
            
            # 绘制安全区域
            if ship.avoidance_active:
                safety_circle = Circle((ship.x, ship.y), 0.003, 
                                     color='yellow', alpha=0.3, linestyle='--')
                self.ax.add_patch(safety_circle)
                self.safety_zones.append(safety_circle)
        
        # 绘制船舶间距离线
        self._draw_ship_distances()
    
    def _draw_ship_distances(self):
        """绘制船舶间距离"""
        ships_list = list(self.ships.values())
        
        for i in range(len(ships_list)):
            for j in range(i + 1, len(ships_list)):
                ship1 = ships_list[i]
                ship2 = ships_list[j]
                
                # 使用旋转卡壳算法计算精确距离
                try:
                    geom1 = ShipGeometry(
                        center=Point(ship1.x, ship1.y),
                        length=ship1.length / 111000,
                        width=ship1.width / 111000,
                        heading=ship1.heading,
                        vertices=[]
                    )
                    
                    geom2 = ShipGeometry(
                        center=Point(ship2.x, ship2.y),
                        length=ship2.length / 111000,
                        width=ship2.width / 111000,
                        heading=ship2.heading,
                        vertices=[]
                    )
                    
                    distance_result = self.calipers.rotating_calipers_distance(
                        geom1.vertices, geom2.vertices
                    )
                    
                    distance_nm = distance_result['min_distance'] * 60
                    closest_points = distance_result['closest_points']
                    
                    if closest_points['ship1'] and closest_points['ship2']:
                        cp1 = closest_points['ship1']
                        cp2 = closest_points['ship2']
                        
                        # 根据距离设置颜色
                        if distance_nm < 1.0:
                            color = 'red'
                            alpha = 0.9
                        elif distance_nm < 2.0:
                            color = 'orange'
                            alpha = 0.7
                        elif distance_nm < 4.0:
                            color = 'yellow'
                            alpha = 0.5
                        else:
                            color = 'green'
                            alpha = 0.3
                        
                        # 绘制距离线
                        line, = self.ax.plot([cp1.x, cp2.x], [cp1.y, cp2.y],
                                           color=color, linewidth=2, alpha=alpha)
                        self.conflict_markers.append(line)
                        
                        # 标记距离
                        mid_x = (cp1.x + cp2.x) / 2
                        mid_y = (cp1.y + cp2.y) / 2
                        text = self.ax.annotate(f'{distance_nm:.1f}nm', (mid_x, mid_y),
                                              xytext=(0, 0), textcoords='offset points',
                                              fontsize=8, ha='center',
                                              bbox=dict(boxstyle='round,pad=0.2', 
                                                       facecolor=color, alpha=0.7))
                        self.conflict_markers.append(text)
                
                except Exception as e:
                    print(f"Distance calculation failed: {e}")
    
    def _generate_info_text(self) -> str:
        """生成信息文本"""
        active_ships = len(self.ships)
        avoiding_ships = sum(1 for ship in self.ships.values() if ship.avoidance_active)
        
        coordination_status = "ACTIVE" if self.coordination_active else "MONITORING"
        
        info_text = f"""DYNAMIC COLLISION AVOIDANCE STATUS

Time: {self.current_time:.1f}s
Ships: {active_ships} | Avoiding: {avoiding_ships}
Coordination: {coordination_status}

Algorithm: Rotating Calipers
Performance: Real-time
COLREGs: Compliant
Safety: Multi-layer"""
        
        return info_text
    
    def update_frame(self, frame):
        """更新动画帧"""
        self.frame_count = frame
        self.current_time = frame * self.dt
        
        # 预测所有船舶轨迹
        for ship in self.ships.values():
            self._predict_ship_trajectory(ship)
        
        # 检测是否需要启动协调
        if self._detect_coordination_trigger():
            self._execute_coordination()
        
        # 更新船舶位置
        for ship in self.ships.values():
            self._update_ship_position(ship, self.dt)
        
        # 更新可视化元素
        self._update_visualization_elements()
        
        # 更新信息文本
        info_text = self._generate_info_text()
        self.info_text.set_text(info_text)
        
        return (list(self.ship_patches.values()) + 
                list(self.trajectory_lines.values()) + 
                list(self.prediction_lines.values()) + 
                [self.info_text])
    
    def create_animation(self, frames=150, interval=400):
        """创建动画"""
        self.max_frames = frames
        
        anim = animation.FuncAnimation(
            self.fig, self.update_frame, frames=frames,
            interval=interval, blit=False, repeat=True
        )
        
        return anim
