"""
Ferry Crossing Demo
Complex Multi-Vessel Ferry Crossing Scenario
North-South Main Channel vs East-West Ferry Crossings
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon, Circle, Rectangle
import math
import warnings

warnings.filterwarnings('ignore')
plt.rcParams['font.family'] = 'DejaVu Sans'

from src_clean.geometry.advanced_rotating_calipers import (
    Point, VesselState, AdvancedRotatingCalipers, CollisionRisk
)
from src_clean.coordination.geometric_collision_avoidance import (
    GeometricCollisionAvoidance, AvoidanceStrategy
)

def create_ferry_crossing_scenario():
    """Create complex ferry crossing scenario"""

    current_time = 1640995200.0
    vessel_states = []

    # North-South Main Channel Vessels (Priority)
    # 1. Northbound Container Ship
    vessel_states.append(VesselState(
        position=Point(121.010, 30.995),
        velocity=Point(0.0, 0.00025),
        heading=0.0,
        angular_velocity=0.0,
        length=400.0, width=58.0,
        timestamp=current_time
    ))

    # 2. Northbound Bulk Carrier
    vessel_states.append(VesselState(
        position=Point(121.010, 31.000),
        velocity=Point(0.0, 0.0002),
        heading=0.0,
        angular_velocity=0.0,
        length=280.0, width=45.0,
        timestamp=current_time
    ))

    # 3. Southbound Tanker
    vessel_states.append(VesselState(
        position=Point(121.012, 31.020),
        velocity=Point(0.0, -0.00018),
        heading=180.0,
        angular_velocity=0.0,
        length=350.0, width=60.0,
        timestamp=current_time
    ))

    # 4. Southbound Cargo Ship
    vessel_states.append(VesselState(
        position=Point(121.012, 31.015),
        velocity=Point(0.0, -0.0002),
        heading=180.0,
        angular_velocity=0.0,
        length=220.0, width=32.0,
        timestamp=current_time
    ))

    # East-West Ferry Crossings (Must Give Way)
    # 5. Eastbound Ferry 1
    vessel_states.append(VesselState(
        position=Point(121.000, 31.008),
        velocity=Point(0.00022, 0.0),
        heading=90.0,
        angular_velocity=0.0,
        length=180.0, width=28.0,
        timestamp=current_time
    ))

    # 6. Eastbound Ferry 2
    vessel_states.append(VesselState(
        position=Point(120.998, 31.005),
        velocity=Point(0.0002, 0.0),
        heading=90.0,
        angular_velocity=0.0,
        length=160.0, width=25.0,
        timestamp=current_time
    ))

    # 7. Westbound Ferry 1
    vessel_states.append(VesselState(
        position=Point(121.025, 31.012),
        velocity=Point(-0.00018, 0.0),
        heading=270.0,
        angular_velocity=0.0,
        length=170.0, width=26.0,
        timestamp=current_time
    ))

    # 8. Westbound Ferry 2
    vessel_states.append(VesselState(
        position=Point(121.022, 31.006),
        velocity=Point(-0.0002, 0.0),
        heading=270.0,
        angular_velocity=0.0,
        length=150.0, width=24.0,
        timestamp=current_time
    ))

    return vessel_states

def analyze_scenario():
    """Analyze the ferry crossing scenario"""

    print("Ferry Crossing Scenario Analysis")
    print("="*60)
    print("North-South Main Channel vs East-West Ferry Crossings")

    vessel_states = create_ferry_crossing_scenario()

    # Classify vessels
    ns_vessels = []  # North-South main channel
    ew_vessels = []  # East-West crossing

    for i, vessel in enumerate(vessel_states):
        if vessel.heading in [0.0, 180.0]:
            ns_vessels.append((i, vessel))
        else:
            ew_vessels.append((i, vessel))

    print(f"\nScenario Composition:")
    print(f"  North-South Main Channel: {len(ns_vessels)} vessels")
    print(f"  East-West Crossing: {len(ew_vessels)} vessels")
    print(f"  Total Vessels: {len(vessel_states)} vessels")

    # Show vessel details
    print(f"\nNorth-South Main Channel Vessels (Priority):")
    for i, vessel in ns_vessels:
        direction = "Northbound" if vessel.heading == 0.0 else "Southbound"
        speed_knots = vessel.velocity.magnitude() * 111000 / 0.514444
        print(f"  Vessel {i+1}: {vessel.length}m x {vessel.width}m, "
              f"{speed_knots:.1f} knots, {direction}")

    print(f"\nEast-West Crossing Vessels (Must Give Way):")
    for i, vessel in ew_vessels:
        direction = "Eastbound" if vessel.heading == 90.0 else "Westbound"
        speed_knots = vessel.velocity.magnitude() * 111000 / 0.514444
        vessel_type = "High-speed Ferry" if vessel.length < 130 else "Car Ferry"
        print(f"  Vessel {i+1}: {vessel.length}m x {vessel.width}m, "
              f"{speed_knots:.1f} knots, {direction}")

    # Create avoidance system
    avoidance_system = GeometricCollisionAvoidance(safety_domain_radius=0.002)

    print(f"\nExecuting Multi-Vessel Coordination Analysis...")

    # Execute coordination analysis
    coordination_result = avoidance_system.execute_dynamic_coordination(
        vessel_states, time_horizon=600.0
    )

    if 'error' in coordination_result:
        print(f"Analysis failed: {coordination_result['error']}")
        return None

    # Analyze results
    geometry_analysis = coordination_result['geometry_analysis']
    avoidance_maneuvers = coordination_result['avoidance_maneuvers']

    print(f"\nDistance Analysis Results:")
    envelope_analysis = geometry_analysis['envelope_analysis']

    for pair_key, data in envelope_analysis['minimum_separation_matrix'].items():
        distance_nm = data['distance'] * 60
        risk_level = data['risk_level']

        if risk_level in [CollisionRisk.WARNING, CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
            # Extract vessel IDs from pair key like "vessel_0_vessel_1"
            parts = pair_key.split('_')
            if len(parts) >= 4:
                try:
                    v1 = int(parts[1])
                    v2 = int(parts[3])
                    v1_type = "Main Channel" if vessel_states[v1].heading in [0.0, 180.0] else "Crossing"
                    v2_type = "Main Channel" if vessel_states[v2].heading in [0.0, 180.0] else "Crossing"

                    print(f"  Vessel {v1+1}({v1_type}) <-> Vessel {v2+1}({v2_type}): "
                          f"{distance_nm:.2f}nm, {risk_level.value}")
                except (ValueError, IndexError):
                    print(f"  {pair_key}: {distance_nm:.2f}nm, {risk_level.value}")

    print(f"\nAvoidance Strategy Analysis:")
    ns_maneuvers = 0
    ew_maneuvers = 0

    for maneuver in avoidance_maneuvers:
        vessel = vessel_states[maneuver.vessel_id]
        vessel_type = "Main Channel" if vessel.heading in [0.0, 180.0] else "Crossing"

        if maneuver.strategy != AvoidanceStrategy.MAINTAIN_COURSE:
            print(f"  Vessel {maneuver.vessel_id+1}({vessel_type}): "
                  f"{maneuver.strategy.value}")
            print(f"    Heading Change: {maneuver.heading_change:+.1f} degrees")
            print(f"    Speed Change: {maneuver.speed_change:+.1%}")
            print(f"    Priority: {maneuver.priority.value}")

            if vessel_type == "Main Channel":
                ns_maneuvers += 1
            else:
                ew_maneuvers += 1

    print(f"\nCOLREGs Rule 9 Compliance Analysis:")
    print(f"  Main Channel vessels requiring avoidance: {ns_maneuvers}")
    print(f"  Crossing vessels requiring avoidance: {ew_maneuvers}")

    if ns_maneuvers == 0:
        print(f"  Status: FULLY COMPLIANT - Crossing vessels do not impede main channel")
    elif ns_maneuvers < ew_maneuvers:
        print(f"  Status: MOSTLY COMPLIANT - Primarily crossing vessels give way")
    else:
        print(f"  Status: NON-COMPLIANT - Too many main channel vessels giving way")

    # Performance evaluation
    performance = coordination_result['performance_metrics']
    print(f"\nSystem Performance:")
    print(f"  Safety Improvement: {performance['safety_improvement']:.1%}")
    print(f"  Coordination Quality: {performance['coordination_quality']:.1%}")
    print(f"  Computation Time: {performance['computational_time']*1000:.1f}ms")

    return {
        'vessel_states': vessel_states,
        'coordination_result': coordination_result,
        'ns_vessels': ns_vessels,
        'ew_vessels': ew_vessels
    }

def visualize_scenario(analysis_result):
    """Visualize the ferry crossing scenario"""

    if not analysis_result:
        return

    vessel_states = analysis_result['vessel_states']
    coordination_result = analysis_result['coordination_result']
    ns_vessels = analysis_result['ns_vessels']
    ew_vessels = analysis_result['ew_vessels']

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 16))

    # Plot 1: Overall scenario
    ax1.set_title('Ferry Crossing Scenario\nNorth-South Channel vs East-West Ferries',
                  fontweight='bold')
    plot_overall_scenario(ax1, vessel_states, ns_vessels, ew_vessels)

    # Plot 2: Collision risks
    ax2.set_title('Collision Risk Analysis\nRotating Calipers Assessment',
                  fontweight='bold')
    plot_collision_risks(ax2, vessel_states, coordination_result)

    # Plot 3: Avoidance strategies
    ax3.set_title('Avoidance Strategies\nCOLREGs Rule 9 Compliance',
                  fontweight='bold')
    plot_avoidance_strategies(ax3, vessel_states, coordination_result)

    # Plot 4: Trajectory prediction
    ax4.set_title('Trajectory Prediction\nSpatio-Temporal Analysis',
                  fontweight='bold')
    plot_trajectory_prediction(ax4, vessel_states)

    plt.tight_layout()
    plt.savefig('ferry_crossing_scenario.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_overall_scenario(ax, vessel_states, ns_vessels, ew_vessels):
    """Plot the overall scenario"""

    # Draw main channel
    channel_rect = Rectangle((121.008, 30.990), 0.006, 0.035,
                            linewidth=3, edgecolor='blue', facecolor='lightblue',
                            alpha=0.3, label='Main Channel (N-S)')
    ax.add_patch(channel_rect)

    # Draw channel centerline
    ax.plot([121.011, 121.011], [30.990, 31.025], 'b--',
           linewidth=2, alpha=0.8, label='Channel Centerline')

    # Draw ferry routes
    ferry_lines_y = [31.003, 31.005, 31.006, 31.008, 31.010, 31.012]
    for y in ferry_lines_y:
        ax.plot([120.995, 121.030], [y, y], 'r:', linewidth=1, alpha=0.6)
    ax.plot([120.995, 121.030], [ferry_lines_y[0], ferry_lines_y[0]], 'r:',
           linewidth=1, alpha=0.6, label='Ferry Routes (E-W)')

    # Draw North-South vessels
    for i, vessel in ns_vessels:
        color = 'darkblue' if vessel.heading == 0.0 else 'navy'
        marker = '^' if vessel.heading == 0.0 else 'v'

        ax.scatter(vessel.position.x, vessel.position.y, s=300,
                  color=color, marker=marker, alpha=0.9, zorder=10,
                  edgecolors='white', linewidth=2)

        direction = "N" if vessel.heading == 0.0 else "S"
        ax.annotate(f'{i+1}{direction}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points',
                   ha='center', va='center', fontweight='bold',
                   color='white', fontsize=8)

    # Draw East-West vessels
    for i, vessel in ew_vessels:
        color = 'darkred' if vessel.heading == 90.0 else 'maroon'
        marker = '>' if vessel.heading == 90.0 else '<'

        ax.scatter(vessel.position.x, vessel.position.y, s=200,
                  color=color, marker=marker, alpha=0.9, zorder=10,
                  edgecolors='white', linewidth=1)

        direction = "E" if vessel.heading == 90.0 else "W"
        ax.annotate(f'{i+1}{direction}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points',
                   ha='center', va='center', fontweight='bold',
                   color='white', fontsize=7)

    setup_maritime_plot(ax)
    ax.legend(loc='upper left')

def plot_collision_risks(ax, vessel_states, coordination_result):
    """Plot collision risks"""

    # Draw all vessels
    for i, vessel in enumerate(vessel_states):
        if vessel.heading in [0.0, 180.0]:
            color = 'blue'
            marker = 'o'
        else:
            color = 'red'
            marker = 's'

        ax.scatter(vessel.position.x, vessel.position.y, s=150,
                  color=color, marker=marker, alpha=0.8, zorder=10)
        ax.annotate(f'{i+1}', (vessel.position.x, vessel.position.y),
                   xytext=(0, 0), textcoords='offset points',
                   ha='center', va='center', fontweight='bold',
                   color='white', fontsize=8)

    # Draw risk lines
    envelope_analysis = coordination_result['geometry_analysis']['envelope_analysis']

    for pair_key, data in envelope_analysis['minimum_separation_matrix'].items():
        distance_nm = data['distance'] * 60
        risk_level = data['risk_level']

        if risk_level in [CollisionRisk.WARNING, CollisionRisk.CRITICAL, CollisionRisk.IMMINENT]:
            # Extract vessel IDs from pair key like "vessel_0_vessel_1"
            parts = pair_key.split('_')
            if len(parts) >= 4:
                try:
                    v1 = int(parts[1])
                    v2 = int(parts[3])
                    if v1 < len(vessel_states) and v2 < len(vessel_states):
                        pos1 = vessel_states[v1].position
                        pos2 = vessel_states[v2].position

                        if risk_level == CollisionRisk.IMMINENT:
                            color = 'red'
                            width = 4
                        elif risk_level == CollisionRisk.CRITICAL:
                            color = 'orange'
                            width = 3
                        else:
                            color = 'yellow'
                            width = 2

                        ax.plot([pos1.x, pos2.x], [pos1.y, pos2.y],
                               color=color, linewidth=width, alpha=0.8)

                        mid_x = (pos1.x + pos2.x) / 2
                        mid_y = (pos1.y + pos2.y) / 2
                        ax.annotate(f'{distance_nm:.1f}nm', (mid_x, mid_y),
                                   fontsize=8, ha='center',
                                   bbox=dict(boxstyle='round,pad=0.2',
                                            facecolor=color, alpha=0.7))
                except (ValueError, IndexError):
                    pass

    setup_maritime_plot(ax)

def plot_avoidance_strategies(ax, vessel_states, coordination_result):
    """Plot avoidance strategies"""

    avoidance_maneuvers = coordination_result['avoidance_maneuvers']

    for i, (vessel, maneuver) in enumerate(zip(vessel_states, avoidance_maneuvers)):
        if vessel.heading in [0.0, 180.0]:
            base_color = 'blue'
        else:
            base_color = 'red'

        if maneuver.strategy == AvoidanceStrategy.MAINTAIN_COURSE:
            color = base_color
            alpha = 0.6
            size = 100
        else:
            color = 'orange' if maneuver.priority.value >= 3 else 'yellow'
            alpha = 0.9
            size = 200

        ax.scatter(vessel.position.x, vessel.position.y, s=size,
                  color=color, alpha=alpha, zorder=10,
                  edgecolors='black', linewidth=1)

        if maneuver.strategy != AvoidanceStrategy.MAINTAIN_COURSE:
            strategy_text = f"V{i+1}\n{maneuver.strategy.value[:8]}\n"
            strategy_text += f"H:{maneuver.heading_change:+.0f}deg"

            ax.annotate(strategy_text, (vessel.position.x, vessel.position.y),
                       xytext=(15, 15), textcoords='offset points',
                       fontsize=7, ha='left',
                       bbox=dict(boxstyle='round,pad=0.3',
                                facecolor=color, alpha=0.8))
        else:
            ax.annotate(f'{i+1}', (vessel.position.x, vessel.position.y),
                       xytext=(0, 0), textcoords='offset points',
                       ha='center', va='center', fontweight='bold',
                       color='white', fontsize=8)

    setup_maritime_plot(ax)

def plot_trajectory_prediction(ax, vessel_states):
    """Plot trajectory prediction"""

    time_horizon = 300
    time_steps = np.arange(0, time_horizon, 30)

    for i, vessel in enumerate(vessel_states):
        if vessel.heading in [0.0, 180.0]:
            color = 'blue'
            alpha = 0.8
        else:
            color = 'red'
            alpha = 0.6

        ax.scatter(vessel.position.x, vessel.position.y, s=100,
                  color=color, alpha=1.0, zorder=10)

        traj_x = []
        traj_y = []

        for t in time_steps:
            future_x = vessel.position.x + vessel.velocity.x * t
            future_y = vessel.position.y + vessel.velocity.y * t
            traj_x.append(future_x)
            traj_y.append(future_y)

        ax.plot(traj_x, traj_y, color=color, linestyle='--',
               alpha=alpha, linewidth=2)

        if traj_x and traj_y:
            ax.scatter(traj_x[-1], traj_y[-1], s=50, color=color,
                      marker='x', alpha=alpha)

    setup_maritime_plot(ax)

def setup_maritime_plot(ax):
    """Setup maritime plot"""
    ax.set_xlim(120.99, 121.035)
    ax.set_ylim(30.985, 31.030)
    ax.set_xlabel('Longitude')
    ax.set_ylabel('Latitude')
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal')

def main():
    """Main function"""
    print("Complex Ferry Crossing Scenario")
    print("="*80)
    print("North-South Main Channel vs East-West Ferry Crossings")
    print("COLREGs Rule 9 Verification")

    analysis_result = analyze_scenario()

    if analysis_result:
        visualize_scenario(analysis_result)

        print(f"\nScenario Features:")
        print(f"  - 8 vessels in complex traffic flow")
        print(f"  - North-South main channel bidirectional traffic")
        print(f"  - East-West ferry multi-point crossings")
        print(f"  - COLREGs Rule 9 automatic application")
        print(f"  - Rotating calipers precise calculation")

        print(f"\nOutput: ferry_crossing_scenario.png")
        print(f"Ferry crossing scenario demo completed!")

    else:
        print(f"Scenario analysis failed!")

if __name__ == "__main__":
    main()
